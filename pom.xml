<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.5.3</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>org.auction</groupId>
    <artifactId>auction_system</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>war</packaging>
    <name>auction_system</name>
    <description>auction_system</description>
    <url/>
    <licenses>
        <license/>
    </licenses>
    <developers>
        <developer/>
    </developers>
    <scm>
        <connection/>
        <developerConnection/>
        <tag/>
        <url/>
    </scm>
    <properties>
        <java.version>17</java.version>
    </properties>
    <!-- 定义项目依赖的外部库 -->
    <dependencies>

        <!--
            Spring Boot Web 启动器，提供 Web 开发支持，包括 Spring MVC 和嵌入式 Tomcat。
            支持 REST API、控制器和 JSP 视图。
        -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!--
            Spring Boot JDBC 启动器，提供 JDBC 数据库访问支持。
            包括 JdbcTemplate 和 DataSource 配置，依赖数据库驱动。
        -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>

        <!--
            MySQL 数据库驱动，用于连接 MySQL 数据库。
            与 spring-boot-starter-jdbc 配合，提供数据库访问能力。
        -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <version>9.1.0</version>
        </dependency>

        <!--
            Spring Cloud Config 客户端启动器，支持从配置服务器获取配置。
            当前项目中通过 spring.cloud.config.enabled=false 禁用。
        -->

        <!--
            Tomcat 嵌入式 JSP 引擎，支持 JSP 视图解析。
            与 spring-boot-starter-web 配合，渲染 WEB-INF/views/*.jsp 文件。
        -->
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-jasper</artifactId>
            <version>10.1.30</version>
        </dependency>

        <!--
            Jakarta JSTL API，提供 JSP 页面中的标准标签库支持。
            用于 login.jsp 和 index.jsp 中的 JSTL 标签（如 c:if、c:out）。
        -->
        <dependency>
            <groupId>jakarta.servlet.jsp.jstl</groupId>
            <artifactId>jakarta.servlet.jsp.jstl-api</artifactId>
            <version>3.0.0</version>
        </dependency>

        <!--
            Jakarta JSTL 实现，提供 JSTL 标签的具体功能。
            与 jakarta.servlet.jsp.jstl-api 配合使用。
        -->
        <dependency>
            <groupId>org.glassfish.web</groupId>
            <artifactId>jakarta.servlet.jsp.jstl</artifactId>
            <version>3.0.1</version>
        </dependency>

        <!--
            Jakarta Annotation API，提供注解支持（如 @Resource）。
            用于 UserDAOImpl 等类中的依赖注入。
        -->
        <dependency>
            <groupId>jakarta.annotation</groupId>
            <artifactId>jakarta.annotation-api</artifactId>
            <version>3.0.0</version>
        </dependency>

        <!--
            Spring Boot Tomcat 启动器，提供嵌入式 Tomcat 支持。
            scope=provided 表示仅用于开发和测试，WAR 部署时由外部容器提供。
        -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
            <scope>provided</scope>
        </dependency>

        <!--
            Spring Boot 开发工具，提供热重载和开发便利功能。
            scope=runtime 和 optional=true 表示仅在开发环境使用，不包含在生产构建中。
        -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <scope>runtime</scope>
            <optional>true</optional>
        </dependency>

        <!--
            Spring Boot 测试启动器，提供测试支持（如 JUnit、Mockito）。
            scope=test 表示仅在测试环境中使用。
        -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>nexus-163</id>
            <name>Nexus 163</name>
            <url>http://mirrors.163.com/maven/repository/maven-public/</url>
            <layout>default</layout>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>nexus-163</id>
            <name>Nexus 163</name>
            <url>http://mirrors.163.com/maven/repository/maven-public/</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </pluginRepository>
    </pluginRepositories>
</project>
