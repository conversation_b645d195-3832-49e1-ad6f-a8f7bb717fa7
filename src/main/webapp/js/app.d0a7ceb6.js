(function(){"use strict";var e={8725:function(e,t,s){var a=s(5130),r=s(6768);const o={id:"my-app"};function i(e,t,s,a,i,l){const n=(0,r.g2)("CourseManage");return(0,r.uX)(),(0,r.CE)("div",o,[(0,r.bF)(n)])}var l=s(4232);const n={class:"course-manage"},d={class:"search"},u={key:0},c={key:1},p={class:"ellipsis"},h=["href"],g={key:1},m={class:"operation-buttons"},f={key:1,style:{display:"block","margin-bottom":"10px"}},b={key:0,style:{"margin-left":"10px"}},v={key:1,style:{"margin-left":"10px"}};function k(e,t,s,o,i,k){const F=(0,r.g2)("el-input"),_=(0,r.g2)("el-form-item"),w=(0,r.g2)("el-button"),y=(0,r.g2)("el-form"),C=(0,r.g2)("el-table-column"),I=(0,r.g2)("el-image"),P=(0,r.g2)("el-table"),z=(0,r.g2)("el-pagination"),V=(0,r.g2)("el-upload"),x=(0,r.g2)("el-option"),U=(0,r.g2)("el-select"),O=(0,r.g2)("el-dialog");return(0,r.uX)(),(0,r.CE)("div",n,[t[23]||(t[23]=(0,r.Lk)("div",{class:"location"},[(0,r.Lk)("strong",null,"当前位置："),(0,r.Lk)("span",null,"课程管理页面")],-1)),(0,r.Lk)("div",d,[(0,r.bF)(y,{inline:!0,model:i.searchForm,onSubmit:t[1]||(t[1]=(0,a.D$)(()=>{},["prevent"]))},{default:(0,r.k6)(()=>[(0,r.bF)(_,{label:"课程标题"},{default:(0,r.k6)(()=>[(0,r.bF)(F,{modelValue:i.searchForm.title,"onUpdate:modelValue":t[0]||(t[0]=e=>i.searchForm.title=e),placeholder:"请输入课程标题"},null,8,["modelValue"])]),_:1}),(0,r.bF)(_,null,{default:(0,r.k6)(()=>[(0,r.bF)(w,{type:"primary",onClick:k.searchCourses},{default:(0,r.k6)(()=>t[12]||(t[12]=[(0,r.eW)("查询")])),_:1,__:[12]},8,["onClick"]),(0,r.bF)(w,{type:"success",onClick:k.handleAdd},{default:(0,r.k6)(()=>t[13]||(t[13]=[(0,r.eW)("添加课程")])),_:1,__:[13]},8,["onClick"])]),_:1})]),_:1},8,["model"])]),i.isLoading?((0,r.uX)(),(0,r.CE)("div",u,"加载中...")):(0,r.Q3)("",!0),i.isLoading?(0,r.Q3)("",!0):((0,r.uX)(),(0,r.Wv)(P,{key:1,ref:"table",data:i.courseList,border:"",fit:!0,style:{width:"100%"},onResize:k.handleTableResize},{default:(0,r.k6)(()=>[(0,r.bF)(C,{prop:"title",label:"课程标题","min-width":"150"},{default:(0,r.k6)(({row:e})=>[(0,r.eW)((0,l.v_)(e.title||"无标题"),1)]),_:1}),(0,r.bF)(C,{label:"封面图片","min-width":"120"},{default:(0,r.k6)(({row:e})=>[e.imagePath?((0,r.uX)(),(0,r.Wv)(I,{key:0,style:{width:"100px",height:"60px"},src:e.imagePath,"preview-src-list":[e.imagePath],fit:"cover"},null,8,["src","preview-src-list"])):((0,r.uX)(),(0,r.CE)("span",c,"没有上传封面"))]),_:1}),(0,r.bF)(C,{prop:"description",label:"课程介绍","min-width":"200"},{default:(0,r.k6)(({row:e})=>[(0,r.Lk)("span",p,(0,l.v_)(e.description||"无描述"),1)]),_:1}),(0,r.bF)(C,{prop:"price",label:"价格","min-width":"80"},{default:(0,r.k6)(({row:e})=>[(0,r.eW)((0,l.v_)(k.formatPrice(e.price)),1)]),_:1}),(0,r.bF)(C,{prop:"status",label:"状态","min-width":"80"},{default:(0,r.k6)(({row:e})=>[(0,r.eW)((0,l.v_)("draft"===e.status?"草稿":"published"===e.status?"已发布":"archived"===e.status?"已归档":"未知"),1)]),_:1}),(0,r.bF)(C,{prop:"videoPath",label:"视频","min-width":"150"},{default:(0,r.k6)(({row:e})=>[e.videoPath?((0,r.uX)(),(0,r.CE)("a",{key:0,href:e.videoPath,target:"_blank"},"预览视频",8,h)):((0,r.uX)(),(0,r.CE)("span",g,"无视频"))]),_:1}),(0,r.bF)(C,{label:"操作","min-width":"150"},{default:(0,r.k6)(({row:e})=>[(0,r.Lk)("div",m,[(0,r.bF)(w,{size:"small",onClick:t=>k.handleEdit(e)},{default:(0,r.k6)(()=>t[14]||(t[14]=[(0,r.eW)("编辑")])),_:2,__:[14]},1032,["onClick"]),(0,r.bF)(w,{size:"small",type:"danger",onClick:t=>k.handleDelete(e.courseId)},{default:(0,r.k6)(()=>t[15]||(t[15]=[(0,r.eW)("删除")])),_:2,__:[15]},1032,["onClick"])])]),_:1})]),_:1},8,["data","onResize"])),i.isLoading?(0,r.Q3)("",!0):((0,r.uX)(),(0,r.Wv)(z,{key:2,onSizeChange:k.handleSizeChange,onCurrentChange:k.handleCurrentChange,"current-page":i.pageIndex,"onUpdate:currentPage":t[2]||(t[2]=e=>i.pageIndex=e),"page-sizes":[5,10,15,20],"page-size":i.pageNumber,"onUpdate:pageSize":t[3]||(t[3]=e=>i.pageNumber=e),layout:"total, sizes, prev, pager, next",total:i.total,style:{"margin-top":"20px",display:"flex","justify-content":"center"}},null,8,["onSizeChange","onCurrentChange","current-page","page-size","total"])),(0,r.bF)(O,{title:i.dialogTitle,modelValue:i.dialogVisible,"onUpdate:modelValue":t[9]||(t[9]=e=>i.dialogVisible=e),width:"30%"},{footer:(0,r.k6)(()=>[(0,r.bF)(w,{onClick:t[8]||(t[8]=e=>i.dialogVisible=!1)},{default:(0,r.k6)(()=>t[18]||(t[18]=[(0,r.eW)("取消")])),_:1,__:[18]}),(0,r.bF)(w,{type:"primary",onClick:k.saveCourse},{default:(0,r.k6)(()=>t[19]||(t[19]=[(0,r.eW)("确定")])),_:1,__:[19]},8,["onClick"])]),default:(0,r.k6)(()=>[(0,r.bF)(y,{model:i.courseForm,rules:i.rules,ref:"courseForm","label-width":"100px"},{default:(0,r.k6)(()=>[(0,r.bF)(_,{label:"课程标题",prop:"title"},{default:(0,r.k6)(()=>[(0,r.bF)(F,{modelValue:i.courseForm.title,"onUpdate:modelValue":t[4]||(t[4]=e=>i.courseForm.title=e),placeholder:"请输入课程标题"},null,8,["modelValue"])]),_:1}),(0,r.bF)(_,{label:"封面图片"},{default:(0,r.k6)(()=>[i.previewImageUrl||i.courseForm.imagePath?((0,r.uX)(),(0,r.Wv)(I,{key:0,style:{width:"100px",height:"60px","margin-bottom":"10px"},src:i.previewImageUrl||i.courseForm.imagePath,"preview-src-list":[i.previewImageUrl||i.courseForm.imagePath],fit:"cover"},null,8,["src","preview-src-list"])):((0,r.uX)(),(0,r.CE)("span",f,"未选择图片")),(0,r.bF)(V,{"http-request":k.customImageUpload,"on-change":k.handleImageChange,"before-upload":k.beforeImageUpload,"show-file-list":!1},{default:(0,r.k6)(()=>[(0,r.bF)(w,{size:"small",type:"primary"},{default:(0,r.k6)(()=>t[16]||(t[16]=[(0,r.eW)("选择图片")])),_:1,__:[16]})]),_:1},8,["http-request","on-change","before-upload"])]),_:1}),(0,r.bF)(_,{label:"课程介绍",prop:"description"},{default:(0,r.k6)(()=>[(0,r.bF)(F,{type:"textarea",modelValue:i.courseForm.description,"onUpdate:modelValue":t[5]||(t[5]=e=>i.courseForm.description=e),placeholder:"请输入课程介绍"},null,8,["modelValue"])]),_:1}),(0,r.bF)(_,{label:"价格",prop:"price"},{default:(0,r.k6)(()=>[(0,r.bF)(F,{modelValue:i.courseForm.price,"onUpdate:modelValue":t[6]||(t[6]=e=>i.courseForm.price=e),modelModifiers:{number:!0},placeholder:"请输入价格"},null,8,["modelValue"])]),_:1}),(0,r.bF)(_,{label:"状态",prop:"status"},{default:(0,r.k6)(()=>[(0,r.bF)(U,{modelValue:i.courseForm.status,"onUpdate:modelValue":t[7]||(t[7]=e=>i.courseForm.status=e),placeholder:"请选择状态"},{default:(0,r.k6)(()=>[(0,r.bF)(x,{label:"草稿",value:"draft"}),(0,r.bF)(x,{label:"已发布",value:"published"}),(0,r.bF)(x,{label:"已归档",value:"archived"})]),_:1},8,["modelValue"])]),_:1}),i.courseForm.courseId?((0,r.uX)(),(0,r.Wv)(_,{key:0,label:"上传视频"},{default:(0,r.k6)(()=>[(0,r.bF)(V,{action:`/courseuploadVideo?courseId=${i.courseForm.courseId}`,"on-success":k.handleUploadSuccess,"before-upload":k.beforeUpload,"show-file-list":!1},{default:(0,r.k6)(()=>[(0,r.bF)(w,{size:"small",type:"primary"},{default:(0,r.k6)(()=>t[17]||(t[17]=[(0,r.eW)("点击上传")])),_:1,__:[17]})]),_:1},8,["action","on-success","before-upload"]),i.courseForm.videoPath?((0,r.uX)(),(0,r.CE)("span",b,(0,l.v_)(i.courseForm.videoPath),1)):((0,r.uX)(),(0,r.CE)("span",v,"没有上传视频"))]),_:1})):(0,r.Q3)("",!0)]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),(0,r.bF)(O,{title:"提示",modelValue:i.deleteDialogVisible,"onUpdate:modelValue":t[11]||(t[11]=e=>i.deleteDialogVisible=e),width:"20%"},{footer:(0,r.k6)(()=>[(0,r.bF)(w,{onClick:t[10]||(t[10]=e=>i.deleteDialogVisible=!1)},{default:(0,r.k6)(()=>t[20]||(t[20]=[(0,r.eW)("取消")])),_:1,__:[20]}),(0,r.bF)(w,{type:"primary",onClick:k.confirmDelete},{default:(0,r.k6)(()=>t[21]||(t[21]=[(0,r.eW)("确定")])),_:1,__:[21]},8,["onClick"])]),default:(0,r.k6)(()=>[t[22]||(t[22]=(0,r.Lk)("span",null,"确定要删除该课程吗？",-1))]),_:1,__:[22]},8,["modelValue"])])}s(8111),s(1701);var F=s(4373),_=s(8626),w={name:"CourseManage",data(){return{searchForm:{title:""},courseList:[],pageIndex:1,pageNumber:10,total:0,isLoading:!0,dialogVisible:!1,dialogTitle:"",courseForm:{courseId:null,userId:1,title:"",description:"",price:0,status:"draft",videoPath:"",imagePath:""},previewImageUrl:"",pendingImage:null,rules:{title:[{required:!0,message:"请输入课程标题",trigger:"blur"}],price:[{type:"number",message:"价格必须为数字",trigger:"blur"},{required:!0,message:"请输入价格",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]},deleteDialogVisible:!1,deleteCourseId:null}},mounted(){this.fetchCourses(),this.handleResize=(0,_.throttle)(this.handleResize,100,{leading:!0,trailing:!0}),window.addEventListener("resize",this.handleResize),this.applyResizeObserverPatch()},beforeUnmount(){window.removeEventListener("resize",this.handleResize)},methods:{handleResize(){console.log("Window resized"),this.$nextTick(()=>{this.$refs.table&&this.$refs.table.doLayout()})},handleTableResize(){console.log("Table resized")},applyResizeObserverPatch(){if("undefined"!==typeof window.ResizeObserver){const e=window.ResizeObserver;window.ResizeObserver=class extends e{constructor(e){super((t,s)=>{requestAnimationFrame(()=>{try{e(t,s)}catch(a){console.warn("ResizeObserver callback error:",a)}})})}}}window.addEventListener("error",e=>{e.message.includes("ResizeObserver loop completed with undelivered notifications")&&(console.log("Suppressed ResizeObserver error"),e.preventDefault())})},fetchCourses(){this.isLoading=!0,F.A.get("/courselist",{params:{pageIndex:this.pageIndex,pageNumber:this.pageNumber,title:this.searchForm.title},withCredentials:!0}).then(e=>{console.log("Course list response:",e.data),this.courseList=(e.data.list||[]).map(e=>({...e,title:e.title||"",description:e.description||"",price:void 0!==e.price?e.price:0,status:e.status||"draft",imagePath:e.imagePath||"",videoPath:e.videoPath||""})),this.total=Number(e.data.total)||0,this.pageIndex=Number(e.data.pageIndex)||1,this.pageNumber=Number(e.data.pageNumber)||10}).catch(e=>{console.error("Error fetching courses:",e.response||e),this.$message.error("网络异常，请重试")}).finally(()=>{this.isLoading=!1})},searchCourses(){this.pageIndex=1,this.fetchCourses()},handleSizeChange(e){console.log("Page size changed:",e),this.pageNumber=e,this.fetchCourses()},handleCurrentChange(e){console.log("Page index changed:",e),this.pageIndex=e,this.fetchCourses()},handleAdd(){this.dialogTitle="添加课程",this.courseForm={courseId:null,userId:1,title:"",description:"",price:0,status:"draft",videoPath:"",imagePath:""},this.previewImageUrl="",this.pendingImage=null,this.$refs.courseForm?.resetFields(),this.dialogVisible=!0},handleEdit(e){this.dialogTitle="编辑课程",this.courseForm={...e,price:void 0!==e.price?e.price:0,imagePath:e.imagePath||"",videoPath:e.videoPath||""},this.previewImageUrl="",this.pendingImage=null,this.dialogVisible=!0},async saveCourse(){this.$refs.courseForm.validate(async e=>{if(e){const e=this.courseForm.courseId?"/courseupdate":"/courseadd";try{const t=await F.A.post(e,this.courseForm,{withCredentials:!0});if("OK"===t.data.ok){this.$message.success(this.courseForm.courseId?"修改成功":"添加成功");const e=this.courseForm.courseId||t.data.data?.courseId;if(this.courseForm.courseId=e,this.pendingImage&&e){const t=new FormData;t.append("file",this.pendingImage),t.append("courseId",e);const s=await F.A.post("/courseuploadImage",t,{withCredentials:!0,headers:{"Content-Type":"multipart/form-data"}});"OK"===s.data.ok?(this.courseForm.imagePath=s.data.message,this.$message.success("图片上传成功")):this.$message.error(s.data.message||"图片上传失败")}this.dialogVisible=!1,this.previewImageUrl="",this.pendingImage=null,this.fetchCourses()}else this.$message.error(t.data.message||"操作失败，请重试")}catch(t){console.error("保存课程错误:",t.response||t),this.$message.error("网络异常，请重试")}}})},beforeUpload(e){const t="video/mp4"===e.type,s=e.size/1024/1024<500;return t||this.$message.error("仅支持MP4格式视频！"),s||this.$message.error("视频大小不能超过500MB！"),t&&s},handleUploadSuccess(e){console.log("Video upload response:",e),"OK"===e.ok?(this.courseForm.videoPath=e.message,this.$message.success("视频上传成功")):this.$message.error(e.message||"视频上传失败")},beforeImageUpload(e){const t=["image/jpeg","image/png"].includes(e.type),s=e.size/1024/1024<10;return t||this.$message.error("仅支持JPG或PNG格式图片！"),s||this.$message.error("图片大小不能超过10MB！"),t&&s},handleImageChange(e){this.pendingImage=e.raw;const t=new FileReader;t.onload=e=>{this.previewImageUrl=e.target.result},t.readAsDataURL(e.raw)},customImageUpload(){this.$message.info("图片将在保存课程后上传")},handleImageUploadSuccess(e){console.log("Image upload response:",e),"OK"===e.ok?(this.courseForm.imagePath=e.message,this.previewImageUrl="",this.pendingImage=null,this.$message.success("封面图片上传成功")):this.$message.error(e.message||"图片上传失败")},handleDelete(e){this.deleteCourseId=e,this.deleteDialogVisible=!0},confirmDelete(){F.A.post("/coursedelete",{courseId:this.deleteCourseId},{withCredentials:!0}).then(e=>{"OK"===e.data.ok?(this.$message.success("删除成功"),this.deleteDialogVisible=!1,this.fetchCourses()):this.$message.error(e.data.message||"删除失败，请重试")}).catch(e=>{console.error("Error deleting course:",e.response||e),this.$message.error("网络异常，请重试")})},formatPrice(e){return void 0===e||null===e?"0.00":Number(e).toFixed(2)}}},y=s(1241);const C=(0,y.A)(w,[["render",k],["__scopeId","data-v-14aba3af"]]);var I=C,P={name:"App",components:{CourseManage:I}};const z=(0,y.A)(P,[["render",i]]);var V=z,x=s(3129);s(4188);console.log("main.js loaded");const U=(0,a.Ef)(V);F.A.defaults.baseURL="http://localhost:8080",F.A.defaults.withCredentials=!0,U.config.globalProperties.$axios=F.A,U.use(x.A),U.mount("#my-app"),console.log("Vue app mounted")}},t={};function s(a){var r=t[a];if(void 0!==r)return r.exports;var o=t[a]={id:a,loaded:!1,exports:{}};return e[a].call(o.exports,o,o.exports,s),o.loaded=!0,o.exports}s.m=e,function(){var e=[];s.O=function(t,a,r,o){if(!a){var i=1/0;for(u=0;u<e.length;u++){a=e[u][0],r=e[u][1],o=e[u][2];for(var l=!0,n=0;n<a.length;n++)(!1&o||i>=o)&&Object.keys(s.O).every(function(e){return s.O[e](a[n])})?a.splice(n--,1):(l=!1,o<i&&(i=o));if(l){e.splice(u--,1);var d=r();void 0!==d&&(t=d)}}return t}o=o||0;for(var u=e.length;u>0&&e[u-1][2]>o;u--)e[u]=e[u-1];e[u]=[a,r,o]}}(),function(){s.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return s.d(t,{a:t}),t}}(),function(){s.d=function(e,t){for(var a in t)s.o(t,a)&&!s.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}}(),function(){s.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){s.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){s.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){s.nmd=function(e){return e.paths=[],e.children||(e.children=[]),e}}(),function(){var e={524:0};s.O.j=function(t){return 0===e[t]};var t=function(t,a){var r,o,i=a[0],l=a[1],n=a[2],d=0;if(i.some(function(t){return 0!==e[t]})){for(r in l)s.o(l,r)&&(s.m[r]=l[r]);if(n)var u=n(s)}for(t&&t(a);d<i.length;d++)o=i[d],s.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return s.O(u)},a=self["webpackChunkedu_online_sys"]=self["webpackChunkedu_online_sys"]||[];a.forEach(t.bind(null,0)),a.push=t.bind(null,a.push.bind(a))}();var a=s.O(void 0,[504],function(){return s(8725)});a=s.O(a)})();