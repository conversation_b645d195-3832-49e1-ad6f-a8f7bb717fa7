/**
 * Created by yaling.he on 2015/11/18.
 */
// 时间
function fn() {
    var time = new Date();
    var str = "";
    var div = document.getElementById("time"); // 统一使用 id="time"
    if (!div) return; // 如果元素不存在，退出函数，避免错误

    var year = time.getFullYear();
    var mon = time.getMonth() + 1;
    var day = time.getDate();
    var h = time.getHours();
    var m = time.getMinutes();
    var s = time.getSeconds();
    var week = time.getDay();
    switch (week) {
        case 0: week = "日"; break;
        case 1: week = "一"; break;
        case 2: week = "二"; break;
        case 3: week = "三"; break;
        case 4: week = "四"; break;
        case 5: week = "五"; break;
        case 6: week = "六"; break;
    }
    str = year + "年" + totwo(mon) + "月" + totwo(day) + "日 " + totwo(h) + ":" + totwo(m) + ":" + totwo(s) + " 星期" + week;
    div.innerHTML = str;

    // 动态更新问候语
    var greeting = document.getElementById("greeting");
    if (greeting) {
        if (h < 6) greeting.innerHTML = "凌晨好";
        else if (h < 12) greeting.innerHTML = "上午好";
        else if (h < 15) greeting.innerHTML = "中午好";
        else if (h < 18) greeting.innerHTML = "下午好";
        else if (h < 21) greeting.innerHTML = "傍晚好";
        else greeting.innerHTML = "深夜好";
    }
}
fn(); // 立即执行一次
setInterval(fn, 1000); // 每秒更新

function totwo(n) {
    return n <= 9 ? "0" + n : "" + n;
}