(function(){"use strict";var e={3797:function(e,t,a){var r=a(3751),n=a(641);const o={id:"my-app"};function i(e,t,a,r,i,s){const l=(0,n.g2)("StudentCourseManage");return(0,n.uX)(),(0,n.CE)("div",o,[(0,n.bF)(l)])}var s=a(33);const l={class:"course-manage"},u={class:"search"},c={key:0,class:"loading"},d={key:1},p={class:"ellipsis"};function h(e,t,a,o,i,h){const g=(0,n.g2)("el-input"),f=(0,n.g2)("el-form-item"),m=(0,n.g2)("el-button"),b=(0,n.g2)("el-form"),v=(0,n.g2)("el-table-column"),y=(0,n.g2)("el-image"),C=(0,n.g2)("el-table"),k=(0,n.g2)("el-pagination");return(0,n.uX)(),(0,n.CE)("div",l,[t[6]||(t[6]=(0,n.Lk)("div",{class:"location"},[(0,n.Lk)("strong",null,"当前位置："),(0,n.Lk)("span",null,"课程管理页面")],-1)),(0,n.Lk)("div",u,[(0,n.bF)(b,{inline:!0,model:i.searchForm,onSubmit:t[1]||(t[1]=(0,r.D$)(()=>{},["prevent"]))},{default:(0,n.k6)(()=>[(0,n.bF)(f,{label:"课程标题"},{default:(0,n.k6)(()=>[(0,n.bF)(g,{modelValue:i.searchForm.title,"onUpdate:modelValue":t[0]||(t[0]=e=>i.searchForm.title=e),placeholder:"请输入课程标题",clearable:""},null,8,["modelValue"])]),_:1}),(0,n.bF)(f,null,{default:(0,n.k6)(()=>[(0,n.bF)(m,{type:"primary",onClick:h.searchCourses},{default:(0,n.k6)(()=>t[4]||(t[4]=[(0,n.eW)("查询")])),_:1,__:[4]},8,["onClick"])]),_:1})]),_:1},8,["model"])]),i.isLoading?((0,n.uX)(),(0,n.CE)("div",c,"加载中...")):(0,n.Q3)("",!0),i.isLoading?(0,n.Q3)("",!0):((0,n.uX)(),(0,n.Wv)(C,{key:1,data:i.courseList,border:"",style:{width:"100%"},"default-sort":{prop:"title"}},{default:(0,n.k6)(()=>[(0,n.bF)(v,{prop:"title",label:"课程标题","min-width":"150"},{default:(0,n.k6)(({row:e})=>[(0,n.eW)((0,s.v_)(e.title||"无标题"),1)]),_:1}),(0,n.bF)(v,{label:"封面图片","min-width":"120"},{default:(0,n.k6)(({row:e})=>[e.imagePath?((0,n.uX)(),(0,n.Wv)(y,{key:0,style:{width:"100px",height:"60px"},src:e.imagePath,fit:"cover","preview-src-list":[e.imagePath]},null,8,["src","preview-src-list"])):((0,n.uX)(),(0,n.CE)("span",d,"无封面"))]),_:1}),(0,n.bF)(v,{prop:"description",label:"课程介绍","min-width":"200"},{default:(0,n.k6)(({row:e})=>[(0,n.Lk)("span",p,(0,s.v_)(e.description||"无描述"),1)]),_:1}),(0,n.bF)(v,{prop:"price",label:"价格","min-width":"80"},{default:(0,n.k6)(({row:e})=>[(0,n.eW)((0,s.v_)(h.formatPrice(e.price)),1)]),_:1}),(0,n.bF)(v,{prop:"status",label:"状态","min-width":"80"},{default:(0,n.k6)(({row:e})=>[(0,n.eW)((0,s.v_)("draft"===e.status?"草稿":"published"===e.status?"已发布":"archived"===e.status?"已归档":"未知"),1)]),_:1}),(0,n.bF)(v,{label:"操作","min-width":"100"},{default:(0,n.k6)(({row:e})=>[(0,n.bF)(m,{size:"small",type:"primary",onClick:t=>h.handlePurchase(e)},{default:(0,n.k6)(()=>t[5]||(t[5]=[(0,n.eW)("购买")])),_:2,__:[5]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),!i.isLoading&&i.total>0?((0,n.uX)(),(0,n.Wv)(k,{key:2,"current-page":i.pageIndex,"onUpdate:currentPage":t[2]||(t[2]=e=>i.pageIndex=e),"page-size":i.pageNumber,"onUpdate:pageSize":t[3]||(t[3]=e=>i.pageNumber=e),"page-sizes":[5,10,15,20],layout:"total, sizes, prev, pager, next",total:i.total,onSizeChange:h.handleSizeChange,onCurrentChange:h.handleCurrentChange,style:{"margin-top":"20px","text-align":"center"}},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])):(0,n.Q3)("",!0)])}a(8111),a(1701);var g=a(4335),f={name:"StudentCourseManage",data(){return{searchForm:{title:""},courseList:[],pageIndex:1,pageNumber:10,total:0,isLoading:!1}},mounted(){this.fetchCourses()},methods:{async fetchCourses(){this.isLoading=!0;try{const{data:e}=await g.A.get("/courselist",{params:{pageIndex:this.pageIndex,pageNumber:this.pageNumber,title:this.searchForm.title},withCredentials:!0});this.courseList=(e.list||[]).map(e=>({courseId:e.courseId||0,title:e.title||"",description:e.description||"",price:e.price??0,status:e.status||"draft",imagePath:e.imagePath||""})),this.total=Number(e.total)||0,this.pageIndex=Number(e.pageIndex)||1,this.pageNumber=Number(e.pageNumber)||10}catch(e){console.error("获取课程失败:",e.message,e.response),this.$message.error("获取课程列表失败: "+(e.message||"未知错误")),this.courseList=[{courseId:1,title:"测试课程",description:"测试描述",price:99.99,status:"published",imagePath:"https://via.placeholder.com/100"}],this.total=1}finally{this.isLoading=!1}},searchCourses(){this.pageIndex=1,this.fetchCourses()},handleSizeChange(e){this.pageNumber=e,this.fetchCourses()},handleCurrentChange(e){this.pageIndex=e,this.fetchCourses()},handlePurchase(e){const{courseId:t,title:a,price:r}=e,n=1,o=new URLSearchParams({courseId:t,subjectName:a,price:r.toFixed(2),userId:n}),i=`http://localhost:8686/alipay/pay?${o.toString()}`;console.log("在新窗口打开支付页面:",i),window.open(i,"_blank")},formatPrice(e){return Number(e??0).toFixed(2)}}},m=a(6262);const b=(0,m.A)(f,[["render",h],["__scopeId","data-v-6cf72fba"]]);var v=b,y={name:"App",components:{StudentCourseManage:v}};const C=(0,m.A)(y,[["render",i]]);var k=C,w=a(4359);a(4188);console.log("main.js loaded");const _=(0,r.Ef)(k);g.A.defaults.baseURL="http://localhost:8080",g.A.defaults.withCredentials=!0,_.config.globalProperties.$axios=g.A,_.use(w.A),_.mount("#my-app"),console.log("Vue app mounted")}},t={};function a(r){var n=t[r];if(void 0!==n)return n.exports;var o=t[r]={exports:{}};return e[r].call(o.exports,o,o.exports,a),o.exports}a.m=e,function(){var e=[];a.O=function(t,r,n,o){if(!r){var i=1/0;for(c=0;c<e.length;c++){r=e[c][0],n=e[c][1],o=e[c][2];for(var s=!0,l=0;l<r.length;l++)(!1&o||i>=o)&&Object.keys(a.O).every(function(e){return a.O[e](r[l])})?r.splice(l--,1):(s=!1,o<i&&(i=o));if(s){e.splice(c--,1);var u=n();void 0!==u&&(t=u)}}return t}o=o||0;for(var c=e.length;c>0&&e[c-1][2]>o;c--)e[c]=e[c-1];e[c]=[r,n,o]}}(),function(){a.d=function(e,t){for(var r in t)a.o(t,r)&&!a.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}}(),function(){a.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){a.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){var e={524:0};a.O.j=function(t){return 0===e[t]};var t=function(t,r){var n,o,i=r[0],s=r[1],l=r[2],u=0;if(i.some(function(t){return 0!==e[t]})){for(n in s)a.o(s,n)&&(a.m[n]=s[n]);if(l)var c=l(a)}for(t&&t(r);u<i.length;u++)o=i[u],a.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return a.O(c)},r=self["webpackChunkedu_online_sys"]=self["webpackChunkedu_online_sys"]||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))}();var r=a.O(void 0,[504],function(){return a(3797)});r=a.O(r)})();
//# sourceMappingURL=app.f71a2645.js.map