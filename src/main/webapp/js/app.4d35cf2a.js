(function(){"use strict";var e={6665:function(e,t,o){var s=o(5130),r=o(6768);const a={id:"my-app"};function l(e,t,o,s,l,i){const u=(0,r.g2)("CourseManage");return(0,r.uX)(),(0,r.CE)("div",a,[(0,r.bF)(u)])}var i=o(4232);const u={class:"course-manage"},n={class:"search"},d={key:1},c=["href"],h={key:1},p={class:"operation-buttons"},m={key:1,style:{"margin-top":"10px",display:"block"}},g={key:0};function b(e,t,o,a,l,b){const f=(0,r.g2)("el-input"),F=(0,r.g2)("el-form-item"),k=(0,r.g2)("el-button"),_=(0,r.g2)("el-form"),v=(0,r.g2)("el-table-column"),C=(0,r.g2)("el-image"),y=(0,r.g2)("el-table"),w=(0,r.g2)("el-pagination"),V=(0,r.g2)("el-upload"),I=(0,r.g2)("el-option"),P=(0,r.g2)("el-select"),x=(0,r.g2)("el-dialog");return(0,r.uX)(),(0,r.CE)("div",u,[t[23]||(t[23]=(0,r.Lk)("div",{class:"location"},[(0,r.Lk)("strong",null,"当前位置："),(0,r.Lk)("span",null,"课程管理页面")],-1)),(0,r.Lk)("div",n,[(0,r.bF)(_,{inline:!0,model:l.searchForm,onSubmit:t[1]||(t[1]=(0,s.D$)(()=>{},["prevent"]))},{default:(0,r.k6)(()=>[(0,r.bF)(F,{label:"课程标题"},{default:(0,r.k6)(()=>[(0,r.bF)(f,{modelValue:l.searchForm.title,"onUpdate:modelValue":t[0]||(t[0]=e=>l.searchForm.title=e),placeholder:"请输入课程标题"},null,8,["modelValue"])]),_:1}),(0,r.bF)(F,null,{default:(0,r.k6)(()=>[(0,r.bF)(k,{type:"primary",onClick:b.searchCourses},{default:(0,r.k6)(()=>t[12]||(t[12]=[(0,r.eW)("查询")])),_:1,__:[12]},8,["onClick"]),(0,r.bF)(k,{type:"success",onClick:b.handleAdd},{default:(0,r.k6)(()=>t[13]||(t[13]=[(0,r.eW)("添加课程")])),_:1,__:[13]},8,["onClick"])]),_:1})]),_:1},8,["model"])]),(0,r.bF)(y,{data:l.courseList,border:"",style:{width:"100%"}},{default:(0,r.k6)(()=>[(0,r.bF)(v,{prop:"title",label:"课程标题",width:"200"}),(0,r.bF)(v,{label:"封面图片",width:"150"},{default:(0,r.k6)(({row:e})=>[e.imagePath?((0,r.uX)(),(0,r.Wv)(C,{key:0,style:{width:"100px",height:"60px"},src:e.imagePath,"preview-src-list":[e.imagePath],fit:"cover"},null,8,["src","preview-src-list"])):((0,r.uX)(),(0,r.CE)("span",d,"无图片"))]),_:1}),(0,r.bF)(v,{prop:"description",label:"课程介绍",width:"300"}),(0,r.bF)(v,{prop:"price",label:"价格",width:"100"}),(0,r.bF)(v,{prop:"status",label:"状态",width:"100"},{default:(0,r.k6)(({row:e})=>[(0,r.eW)((0,i.v_)("draft"===e.status?"草稿":"published"===e.status?"已发布":"已归档"),1)]),_:1}),(0,r.bF)(v,{prop:"videoPath",label:"视频",width:"200"},{default:(0,r.k6)(({row:e})=>[e.videoPath?((0,r.uX)(),(0,r.CE)("a",{key:0,href:e.videoPath,target:"_blank"},"预览视频",8,c)):((0,r.uX)(),(0,r.CE)("span",h,"无视频"))]),_:1}),(0,r.bF)(v,{label:"操作",width:"180"},{default:(0,r.k6)(({row:e})=>[(0,r.Lk)("div",p,[(0,r.bF)(k,{size:"small",onClick:t=>b.handleEdit(e)},{default:(0,r.k6)(()=>t[14]||(t[14]=[(0,r.eW)("编辑")])),_:2,__:[14]},1032,["onClick"]),(0,r.bF)(k,{size:"small",type:"danger",onClick:t=>b.handleDelete(e.courseId)},{default:(0,r.k6)(()=>t[15]||(t[15]=[(0,r.eW)("删除")])),_:2,__:[15]},1032,["onClick"])])]),_:1})]),_:1},8,["data"]),(0,r.bF)(w,{onSizeChange:b.handleSizeChange,onCurrentChange:b.handleCurrentChange,"current-page":l.pageIndex,"onUpdate:currentPage":t[2]||(t[2]=e=>l.pageIndex=e),"page-sizes":[5,10,15,20],"page-size":l.pageNumber,"onUpdate:pageSize":t[3]||(t[3]=e=>l.pageNumber=e),layout:"total, sizes, prev, pager, next",total:l.total},null,8,["onSizeChange","onCurrentChange","current-page","page-size","total"]),(0,r.bF)(x,{title:l.dialogTitle,modelValue:l.dialogVisible,"onUpdate:modelValue":t[9]||(t[9]=e=>l.dialogVisible=e),width:"30%"},{footer:(0,r.k6)(()=>[(0,r.bF)(k,{onClick:t[8]||(t[8]=e=>l.dialogVisible=!1)},{default:(0,r.k6)(()=>t[18]||(t[18]=[(0,r.eW)("取消")])),_:1,__:[18]}),(0,r.bF)(k,{type:"primary",onClick:b.saveCourse},{default:(0,r.k6)(()=>t[19]||(t[19]=[(0,r.eW)("确定")])),_:1,__:[19]},8,["onClick"])]),default:(0,r.k6)(()=>[(0,r.bF)(_,{model:l.courseForm,rules:l.rules,ref:"courseForm","label-width":"100px"},{default:(0,r.k6)(()=>[(0,r.bF)(F,{label:"课程标题",prop:"title"},{default:(0,r.k6)(()=>[(0,r.bF)(f,{modelValue:l.courseForm.title,"onUpdate:modelValue":t[4]||(t[4]=e=>l.courseForm.title=e)},null,8,["modelValue"])]),_:1}),(0,r.bF)(F,{label:"封面图片"},{default:(0,r.k6)(()=>[(0,r.bF)(V,{action:l.courseForm.courseId?`/courseuploadImage?courseId=${l.courseForm.courseId}`:"#","on-success":b.handleImageUploadSuccess,"before-upload":b.beforeImageUpload,"show-file-list":!1,disabled:!l.courseForm.courseId},{default:(0,r.k6)(()=>[(0,r.bF)(k,{size:"small",type:"primary",disabled:!l.courseForm.courseId},{default:(0,r.k6)(()=>t[16]||(t[16]=[(0,r.eW)("上传封面")])),_:1,__:[16]},8,["disabled"])]),_:1},8,["action","on-success","before-upload","disabled"]),l.courseForm.imagePath?((0,r.uX)(),(0,r.Wv)(C,{key:0,style:{width:"100px",height:"60px","margin-top":"10px"},src:l.courseForm.imagePath,"preview-src-list":[l.courseForm.imagePath],fit:"cover"},null,8,["src","preview-src-list"])):((0,r.uX)(),(0,r.CE)("span",m,"未上传封面"))]),_:1}),(0,r.bF)(F,{label:"课程介绍",prop:"description"},{default:(0,r.k6)(()=>[(0,r.bF)(f,{type:"textarea",modelValue:l.courseForm.description,"onUpdate:modelValue":t[5]||(t[5]=e=>l.courseForm.description=e)},null,8,["modelValue"])]),_:1}),(0,r.bF)(F,{label:"价格",prop:"price"},{default:(0,r.k6)(()=>[(0,r.bF)(f,{modelValue:l.courseForm.price,"onUpdate:modelValue":t[6]||(t[6]=e=>l.courseForm.price=e),modelModifiers:{number:!0}},null,8,["modelValue"])]),_:1}),(0,r.bF)(F,{label:"状态",prop:"status"},{default:(0,r.k6)(()=>[(0,r.bF)(P,{modelValue:l.courseForm.status,"onUpdate:modelValue":t[7]||(t[7]=e=>l.courseForm.status=e),placeholder:"请选择状态"},{default:(0,r.k6)(()=>[(0,r.bF)(I,{label:"草稿",value:"draft"}),(0,r.bF)(I,{label:"已发布",value:"published"}),(0,r.bF)(I,{label:"已归档",value:"archived"})]),_:1},8,["modelValue"])]),_:1}),l.courseForm.courseId?((0,r.uX)(),(0,r.Wv)(F,{key:0,label:"上传视频"},{default:(0,r.k6)(()=>[(0,r.bF)(V,{action:`/courseuploadVideo?courseId=${l.courseForm.courseId}`,"on-success":b.handleUploadSuccess,"before-upload":b.beforeUpload,"show-file-list":!1},{default:(0,r.k6)(()=>[(0,r.bF)(k,{size:"small",type:"primary"},{default:(0,r.k6)(()=>t[17]||(t[17]=[(0,r.eW)("点击上传")])),_:1,__:[17]})]),_:1},8,["action","on-success","before-upload"]),l.courseForm.videoPath?((0,r.uX)(),(0,r.CE)("span",g,(0,i.v_)(l.courseForm.videoPath),1)):(0,r.Q3)("",!0)]),_:1})):(0,r.Q3)("",!0)]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),(0,r.bF)(x,{title:"提示",modelValue:l.deleteDialogVisible,"onUpdate:modelValue":t[11]||(t[11]=e=>l.deleteDialogVisible=e),width:"20%"},{footer:(0,r.k6)(()=>[(0,r.bF)(k,{onClick:t[10]||(t[10]=e=>l.deleteDialogVisible=!1)},{default:(0,r.k6)(()=>t[20]||(t[20]=[(0,r.eW)("取消")])),_:1,__:[20]}),(0,r.bF)(k,{type:"primary",onClick:b.confirmDelete},{default:(0,r.k6)(()=>t[21]||(t[21]=[(0,r.eW)("确定")])),_:1,__:[21]},8,["onClick"])]),default:(0,r.k6)(()=>[t[22]||(t[22]=(0,r.Lk)("span",null,"确定要删除该课程吗？",-1))]),_:1,__:[22]},8,["modelValue"])])}var f=o(4373),F=o(8626),k={data(){return{searchForm:{title:""},courseList:[],pageIndex:1,pageNumber:10,total:0,dialogVisible:!1,dialogTitle:"",courseForm:{courseId:null,userId:1,title:"",description:"",price:0,status:"draft",videoPath:"",imagePath:""},rules:{title:[{required:!0,message:"请输入课程标题",trigger:"blur"}],price:[{type:"number",message:"价格必须为数字",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]},deleteDialogVisible:!1,deleteCourseId:null}},mounted(){this.fetchCourses(),this.handleResize=(0,F.debounce)(this.handleResize,100),window.addEventListener("resize",this.handleResize)},beforeUnmount(){window.removeEventListener("resize",this.handleResize)},methods:{handleResize(){},fetchCourses(){f.A.get("/courselist",{params:{pageIndex:this.pageIndex,pageNumber:this.pageNumber,title:this.searchForm.title}}).then(e=>{this.courseList=e.data.list,this.total=Number(e.data.total),this.pageIndex=Number(e.data.pageIndex),this.pageNumber=Number(e.data.pageNumber)}).catch(e=>{console.error("Error fetching courses:",e.response||e),this.$message.error("网络异常，请重试")})},searchCourses(){this.pageIndex=1,this.fetchCourses()},handleSizeChange(e){this.pageNumber=e,this.fetchCourses()},handleCurrentChange(e){this.pageIndex=e,this.fetchCourses()},handleAdd(){this.dialogTitle="添加课程",this.courseForm={courseId:null,userId:1,title:"",description:"",price:0,status:"draft",videoPath:"",imagePath:""},this.$refs.courseForm?.resetFields(),this.dialogVisible=!0},handleEdit(e){this.dialogTitle="编辑课程",this.courseForm={...e},this.dialogVisible=!0},saveCourse(){this.$refs.courseForm.validate(e=>{if(e){const e=this.courseForm.courseId?"/courseupdate":"/courseadd";f.A.post(e,this.courseForm).then(e=>{"OK"===e.data.ok?(this.$message.success(this.courseForm.courseId?"修改成功":"添加成功"),this.dialogVisible=!1,this.fetchCourses()):this.$message.error(e.data.message||"操作失败，请重试")}).catch(()=>{this.$message.error("网络异常，请重试")})}})},beforeUpload(e){const t="video/mp4"===e.type,o=e.size/1024/1024<500;return t||this.$message.error("仅支持MP4格式视频！"),o||this.$message.error("视频大小不能超过500MB！"),t&&o},handleUploadSuccess(e){"OK"===e.ok?(this.courseForm.videoPath=e.message,this.$message.success("视频上传成功")):this.$message.error(e.message||"视频上传失败")},beforeImageUpload(e){const t=["image/jpeg","image/png"].includes(e.type),o=e.size/1024/1024<10;return t||this.$message.error("仅支持JPG或PNG格式图片！"),o||this.$message.error("图片大小不能超过10MB！"),t&&o},handleImageUploadSuccess(e){"OK"===e.ok?(this.courseForm.imagePath=e.message,this.$message.success("封面图片上传成功")):this.$message.error(e.message||"图片上传失败")},handleDelete(e){this.deleteCourseId=e,this.deleteDialogVisible=!0},confirmDelete(){f.A.post("/coursedelete",{courseId:this.deleteCourseId}).then(e=>{"OK"===e.data.ok?(this.$message.success("删除成功"),this.deleteDialogVisible=!1,this.fetchCourses()):this.$message.error(e.data.message||"删除失败，请重试")}).catch(()=>{this.$message.error("网络异常，请重试")})}}},_=o(1241);const v=(0,_.A)(k,[["render",b],["__scopeId","data-v-25103979"]]);var C=v,y={name:"App",components:{CourseManage:C}};const w=(0,_.A)(y,[["render",l]]);var V=w,I=o(3129);o(4188);console.log("main.js loaded");const P=(0,s.Ef)(V);f.A.defaults.baseURL="http://localhost:8080",f.A.defaults.withCredentials=!0,P.config.globalProperties.$axios=f.A,P.use(I.A),P.mount("#my-app"),console.log("Vue app mounted")}},t={};function o(s){var r=t[s];if(void 0!==r)return r.exports;var a=t[s]={id:s,loaded:!1,exports:{}};return e[s].call(a.exports,a,a.exports,o),a.loaded=!0,a.exports}o.m=e,function(){var e=[];o.O=function(t,s,r,a){if(!s){var l=1/0;for(d=0;d<e.length;d++){s=e[d][0],r=e[d][1],a=e[d][2];for(var i=!0,u=0;u<s.length;u++)(!1&a||l>=a)&&Object.keys(o.O).every(function(e){return o.O[e](s[u])})?s.splice(u--,1):(i=!1,a<l&&(l=a));if(i){e.splice(d--,1);var n=r();void 0!==n&&(t=n)}}return t}a=a||0;for(var d=e.length;d>0&&e[d-1][2]>a;d--)e[d]=e[d-1];e[d]=[s,r,a]}}(),function(){o.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return o.d(t,{a:t}),t}}(),function(){o.d=function(e,t){for(var s in t)o.o(t,s)&&!o.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}}(),function(){o.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){o.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){o.nmd=function(e){return e.paths=[],e.children||(e.children=[]),e}}(),function(){var e={524:0};o.O.j=function(t){return 0===e[t]};var t=function(t,s){var r,a,l=s[0],i=s[1],u=s[2],n=0;if(l.some(function(t){return 0!==e[t]})){for(r in i)o.o(i,r)&&(o.m[r]=i[r]);if(u)var d=u(o)}for(t&&t(s);n<l.length;n++)a=l[n],o.o(e,a)&&e[a]&&e[a][0](),e[a]=0;return o.O(d)},s=self["webpackChunkedu_online_sys"]=self["webpackChunkedu_online_sys"]||[];s.forEach(t.bind(null,0)),s.push=t.bind(null,s.push.bind(s))}();var s=o.O(void 0,[504],function(){return o(6665)});s=o.O(s)})();