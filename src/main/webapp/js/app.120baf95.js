(function(){"use strict";var e={2090:function(e,t,n){var a=n(5130),o=n(6768);const i={id:"my-app"};function r(e,t,n,a,r,s){const l=(0,o.g2)("StudentList");return(0,o.uX)(),(0,o.CE)("div",i,[(0,o.bF)(l)])}var s=n(4232);const l={class:"student-list"},d={key:0};function u(e,t,n,a,i,r){const u=(0,o.g2)("el-table-column"),c=(0,o.g2)("el-table"),g=(0,o.g2)("el-pagination");return(0,o.uX)(),(0,o.CE)("div",l,[i.isLoading?((0,o.uX)(),(0,o.CE)("div",d,"加载中...")):(0,o.Q3)("",!0),!i.isLoading&&i.tableData.length>0?((0,o.uX)(),(0,o.Wv)(c,{key:1,ref:"table",data:i.tableData,border:"",style:{width:"100%"},onResize:r.handleTableResize},{default:(0,o.k6)(()=>[(0,o.bF)(u,{prop:"username",label:"用户名"}),(0,o.bF)(u,{prop:"email",label:"邮箱"}),(0,o.bF)(u,{prop:"createtime",label:"创建时间"},{default:(0,o.k6)(e=>[(0,o.eW)((0,s.v_)(r.formatDate(e.row.createtime)),1)]),_:1}),(0,o.bF)(u,{prop:"updatetime",label:"更新时间"},{default:(0,o.k6)(e=>[(0,o.eW)((0,s.v_)(r.formatDate(e.row.updatetime)),1)]),_:1})]),_:1},8,["data","onResize"])):(0,o.Q3)("",!0),!i.isLoading&&i.tableData.length>0?((0,o.uX)(),(0,o.Wv)(g,{key:2,"current-page":i.pageIndex,"onUpdate:currentPage":t[0]||(t[0]=e=>i.pageIndex=e),"page-size":i.pageSize,"onUpdate:pageSize":t[1]||(t[1]=e=>i.pageSize=e),"page-sizes":[5,10,20,50],total:i.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:r.handleSizeChange,onCurrentChange:r.handleCurrentChange,style:{"margin-top":"20px"}},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])):(0,o.Q3)("",!0)])}var c=n(8626),g={name:"StudentList",data(){return{tableData:[],pageIndex:1,pageSize:10,total:0,isLoading:!0}},mounted(){console.log("StudentList mounted"),this.fetchStudentList(),this.handleResize=(0,c.throttle)(this.handleResize,100,{leading:!0,trailing:!0}),window.addEventListener("resize",this.handleResize),this.applyResizeObserverPatch()},beforeUnmount(){window.removeEventListener("resize",this.handleResize)},methods:{handleResize(){console.log("Window resized"),this.$nextTick(()=>{this.$refs.table&&this.$refs.table.doLayout()})},handleTableResize(){console.log("Table resized")},applyResizeObserverPatch(){if("undefined"!==typeof window.ResizeObserver){const e=window.ResizeObserver;window.ResizeObserver=class extends e{constructor(e){super((t,n)=>{requestAnimationFrame(()=>{try{e(t,n)}catch(a){console.warn("ResizeObserver callback error:",a)}})})}}}window.addEventListener("error",e=>{e.message.includes("ResizeObserver loop completed with undelivered notifications")&&(console.log("Suppressed ResizeObserver error"),e.preventDefault())})},fetchStudentList(){console.log("Fetching student list..."),this.isLoading=!0,this.$axios.get("/studentList",{params:{pageIndex:this.pageIndex,pageSize:this.pageSize},withCredentials:!0}).then(e=>{console.log("Response:",e.data);const t=e.data;t&&t.list?(this.tableData=t.list,this.total=Number(t.total),this.pageIndex=Number(t.pageIndex),this.pageSize=Number(t.pageNumber)):(this.tableData=[],this.total=0)}).catch(e=>{console.error("获取学员列表失败:",e),this.$message.error("获取学员列表失败，请稍后重试")}).finally(()=>{this.isLoading=!1})},handleSizeChange(e){console.log("Page size changed:",e),this.pageSize=e,this.fetchStudentList()},handleCurrentChange(e){console.log("Page index changed:",e),this.pageIndex=e,this.fetchStudentList()},formatDate(e){if(!e)return"";const t=new Date(e);return`${t.getFullYear()}-${(t.getMonth()+1).toString().padStart(2,"0")}-${t.getDate().toString().padStart(2,"0")} ${t.getHours().toString().padStart(2,"0")}:${t.getMinutes().toString().padStart(2,"0")}:${t.getSeconds().toString().padStart(2,"0")}`}}},p=n(1241);const h=(0,p.A)(g,[["render",u],["__scopeId","data-v-697ba7cf"]]);var f=h,b={name:"App",components:{StudentList:f}};const v=(0,p.A)(b,[["render",r]]);var m=v,z=n(3129),S=(n(4188),n(4373));console.log("main.js loaded");const y=(0,a.Ef)(m);S.A.defaults.baseURL="http://localhost:8080",S.A.defaults.withCredentials=!0,y.config.globalProperties.$axios=S.A,y.use(z.A),y.mount("#my-app"),console.log("Vue app mounted")}},t={};function n(a){var o=t[a];if(void 0!==o)return o.exports;var i=t[a]={id:a,loaded:!1,exports:{}};return e[a].call(i.exports,i,i.exports,n),i.loaded=!0,i.exports}n.m=e,function(){var e=[];n.O=function(t,a,o,i){if(!a){var r=1/0;for(u=0;u<e.length;u++){a=e[u][0],o=e[u][1],i=e[u][2];for(var s=!0,l=0;l<a.length;l++)(!1&i||r>=i)&&Object.keys(n.O).every(function(e){return n.O[e](a[l])})?a.splice(l--,1):(s=!1,i<r&&(r=i));if(s){e.splice(u--,1);var d=o();void 0!==d&&(t=d)}}return t}i=i||0;for(var u=e.length;u>0&&e[u-1][2]>i;u--)e[u]=e[u-1];e[u]=[a,o,i]}}(),function(){n.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return n.d(t,{a:t}),t}}(),function(){n.d=function(e,t){for(var a in t)n.o(t,a)&&!n.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}}(),function(){n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){n.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){n.nmd=function(e){return e.paths=[],e.children||(e.children=[]),e}}(),function(){var e={524:0};n.O.j=function(t){return 0===e[t]};var t=function(t,a){var o,i,r=a[0],s=a[1],l=a[2],d=0;if(r.some(function(t){return 0!==e[t]})){for(o in s)n.o(s,o)&&(n.m[o]=s[o]);if(l)var u=l(n)}for(t&&t(a);d<r.length;d++)i=r[d],n.o(e,i)&&e[i]&&e[i][0](),e[i]=0;return n.O(u)},a=self["webpackChunkedu_online_sys"]=self["webpackChunkedu_online_sys"]||[];a.forEach(t.bind(null,0)),a.push=t.bind(null,a.push.bind(a))}();var a=n.O(void 0,[504],function(){return n(2090)});a=n.O(a)})();