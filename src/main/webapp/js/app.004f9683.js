(function(){"use strict";var e={6909:function(e,t,s){var a=s(5130),i=s(6768);const o={id:"my-app"};function r(e,t,s,a,r,l){const n=(0,i.g2)("CourseManage");return(0,i.uX)(),(0,i.CE)("div",o,[(0,i.bF)(n)])}var l=s(4232);const n={class:"course-manage"},d={class:"search"},u={key:0},c={key:1},p={class:"ellipsis"},h=["href"],m={key:1},g={class:"operation-buttons"},f={key:1,style:{display:"block","margin-bottom":"10px"}},b={key:0,style:{"margin-top":"10px"}},v={key:1,style:{"margin-left":"10px"}};function F(e,t,s,o,r,F){const k=(0,i.g2)("el-input"),y=(0,i.g2)("el-form-item"),_=(0,i.g2)("el-button"),w=(0,i.g2)("el-form"),C=(0,i.g2)("el-table-column"),I=(0,i.g2)("el-image"),P=(0,i.g2)("el-table"),V=(0,i.g2)("el-pagination"),z=(0,i.g2)("el-upload"),x=(0,i.g2)("el-option"),L=(0,i.g2)("el-select"),U=(0,i.g2)("el-dialog");return(0,i.uX)(),(0,i.CE)("div",n,[t[26]||(t[26]=(0,i.Lk)("div",{class:"location"},[(0,i.Lk)("strong",null,"当前位置："),(0,i.Lk)("span",null,"课程管理页面")],-1)),(0,i.Lk)("div",d,[(0,i.bF)(w,{inline:!0,model:r.searchForm,onSubmit:t[1]||(t[1]=(0,a.D$)(()=>{},["prevent"]))},{default:(0,i.k6)(()=>[(0,i.bF)(y,{label:"课程标题"},{default:(0,i.k6)(()=>[(0,i.bF)(k,{modelValue:r.searchForm.title,"onUpdate:modelValue":t[0]||(t[0]=e=>r.searchForm.title=e),placeholder:"请输入课程标题"},null,8,["modelValue"])]),_:1}),(0,i.bF)(y,null,{default:(0,i.k6)(()=>[(0,i.bF)(_,{type:"primary",onClick:F.searchCourses},{default:(0,i.k6)(()=>t[12]||(t[12]=[(0,i.eW)("查询")])),_:1,__:[12]},8,["onClick"]),(0,i.bF)(_,{type:"success",onClick:F.handleAdd},{default:(0,i.k6)(()=>t[13]||(t[13]=[(0,i.eW)("添加课程")])),_:1,__:[13]},8,["onClick"])]),_:1})]),_:1},8,["model"])]),r.isLoading?((0,i.uX)(),(0,i.CE)("div",u,"加载中...")):(0,i.Q3)("",!0),r.isLoading?(0,i.Q3)("",!0):((0,i.uX)(),(0,i.Wv)(P,{key:1,ref:"table",data:r.courseList,border:"",fit:!0,style:{width:"100%"},onResize:F.handleTableResize},{default:(0,i.k6)(()=>[(0,i.bF)(C,{prop:"title",label:"课程标题","min-width":"150"},{default:(0,i.k6)(({row:e})=>[(0,i.eW)((0,l.v_)(e.title||"无标题"),1)]),_:1}),(0,i.bF)(C,{label:"封面图片","min-width":"120"},{default:(0,i.k6)(({row:e})=>[e.imagePath?((0,i.uX)(),(0,i.Wv)(I,{key:0,style:{width:"100px",height:"60px"},src:e.imagePath,"preview-src-list":[e.imagePath],fit:"cover"},null,8,["src","preview-src-list"])):((0,i.uX)(),(0,i.CE)("span",c,"没有上传封面"))]),_:1}),(0,i.bF)(C,{prop:"description",label:"课程介绍","min-width":"200"},{default:(0,i.k6)(({row:e})=>[(0,i.Lk)("span",p,(0,l.v_)(e.description||"无描述"),1)]),_:1}),(0,i.bF)(C,{prop:"price",label:"价格","min-width":"80"},{default:(0,i.k6)(({row:e})=>[(0,i.eW)((0,l.v_)(F.formatPrice(e.price)),1)]),_:1}),(0,i.bF)(C,{prop:"status",label:"状态","min-width":"80"},{default:(0,i.k6)(({row:e})=>[(0,i.eW)((0,l.v_)("draft"===e.status?"草稿":"published"===e.status?"已发布":"archived"===e.status?"已归档":"未知"),1)]),_:1}),(0,i.bF)(C,{prop:"videoPath",label:"视频","min-width":"150"},{default:(0,i.k6)(({row:e})=>[e.videoPath?((0,i.uX)(!0),(0,i.CE)(i.FK,{key:0},(0,i.pI)(e.videoPath.split(","),(e,t)=>((0,i.uX)(),(0,i.CE)("a",{key:t,href:e,target:"_blank",style:{display:"block"}},"预览视频 "+(0,l.v_)(t+1),9,h))),128)):((0,i.uX)(),(0,i.CE)("span",m,"无视频"))]),_:1}),(0,i.bF)(C,{label:"操作","min-width":"150"},{default:(0,i.k6)(({row:e})=>[(0,i.Lk)("div",g,[(0,i.bF)(_,{size:"small",onClick:t=>F.handleEdit(e)},{default:(0,i.k6)(()=>t[14]||(t[14]=[(0,i.eW)("编辑")])),_:2,__:[14]},1032,["onClick"]),(0,i.bF)(_,{size:"small",type:"danger",onClick:t=>F.handleDelete(e.courseId)},{default:(0,i.k6)(()=>t[15]||(t[15]=[(0,i.eW)("删除")])),_:2,__:[15]},1032,["onClick"])])]),_:1})]),_:1},8,["data","onResize"])),r.isLoading?(0,i.Q3)("",!0):((0,i.uX)(),(0,i.Wv)(V,{key:2,onSizeChange:F.handleSizeChange,onCurrentChange:F.handleCurrentChange,"current-page":r.pageIndex,"onUpdate:currentPage":t[2]||(t[2]=e=>r.pageIndex=e),"page-sizes":[5,10,15,20],"page-size":r.pageNumber,"onUpdate:pageSize":t[3]||(t[3]=e=>r.pageNumber=e),layout:"total, sizes, prev, pager, next",total:r.total,style:{"margin-top":"20px",display:"flex","justify-content":"center"}},null,8,["onSizeChange","onCurrentChange","current-page","page-size","total"])),(0,i.bF)(U,{title:r.dialogTitle,modelValue:r.dialogVisible,"onUpdate:modelValue":t[9]||(t[9]=e=>r.dialogVisible=e),width:"30%"},{footer:(0,i.k6)(()=>[(0,i.bF)(_,{onClick:t[8]||(t[8]=e=>r.dialogVisible=!1)},{default:(0,i.k6)(()=>t[21]||(t[21]=[(0,i.eW)("取消")])),_:1,__:[21]}),(0,i.bF)(_,{type:"primary",onClick:F.saveCourse},{default:(0,i.k6)(()=>t[22]||(t[22]=[(0,i.eW)("保存并上传")])),_:1,__:[22]},8,["onClick"])]),default:(0,i.k6)(()=>[(0,i.bF)(w,{model:r.courseForm,rules:r.rules,ref:"courseForm","label-width":"100px"},{default:(0,i.k6)(()=>[(0,i.bF)(y,{label:"课程标题",prop:"title"},{default:(0,i.k6)(()=>[(0,i.bF)(k,{modelValue:r.courseForm.title,"onUpdate:modelValue":t[4]||(t[4]=e=>r.courseForm.title=e),placeholder:"请输入课程标题"},null,8,["modelValue"])]),_:1}),(0,i.bF)(y,{label:"封面图片"},{default:(0,i.k6)(()=>[r.previewImageUrl||r.courseForm.imagePath?((0,i.uX)(),(0,i.Wv)(I,{key:0,style:{width:"100px",height:"60px","margin-bottom":"10px"},src:r.previewImageUrl||r.courseForm.imagePath,"preview-src-list":[r.previewImageUrl||r.courseForm.imagePath],fit:"cover"},null,8,["src","preview-src-list"])):((0,i.uX)(),(0,i.CE)("span",f,"未选择图片")),(0,i.bF)(z,{"http-request":F.customImageUpload,"on-change":F.handleImageChange,"before-upload":F.beforeImageUpload,"show-file-list":!1},{tip:(0,i.k6)(()=>t[17]||(t[17]=[(0,i.Lk)("div",{class:"el-upload__tip"},"图片将在保存课程后上传",-1)])),default:(0,i.k6)(()=>[(0,i.bF)(_,{size:"small",type:"primary"},{default:(0,i.k6)(()=>t[16]||(t[16]=[(0,i.eW)("选择图片")])),_:1,__:[16]})]),_:1},8,["http-request","on-change","before-upload"])]),_:1}),(0,i.bF)(y,{label:"课程介绍",prop:"description"},{default:(0,i.k6)(()=>[(0,i.bF)(k,{type:"textarea",modelValue:r.courseForm.description,"onUpdate:modelValue":t[5]||(t[5]=e=>r.courseForm.description=e),placeholder:"请输入课程介绍"},null,8,["modelValue"])]),_:1}),(0,i.bF)(y,{label:"价格",prop:"price"},{default:(0,i.k6)(()=>[(0,i.bF)(k,{modelValue:r.courseForm.price,"onUpdate:modelValue":t[6]||(t[6]=e=>r.courseForm.price=e),modelModifiers:{number:!0},placeholder:"请输入价格"},null,8,["modelValue"])]),_:1}),(0,i.bF)(y,{label:"状态",prop:"status"},{default:(0,i.k6)(()=>[(0,i.bF)(L,{modelValue:r.courseForm.status,"onUpdate:modelValue":t[7]||(t[7]=e=>r.courseForm.status=e),placeholder:"请选择状态"},{default:(0,i.k6)(()=>[(0,i.bF)(x,{label:"草稿",value:"draft"}),(0,i.bF)(x,{label:"已发布",value:"published"}),(0,i.bF)(x,{label:"已归档",value:"archived"})]),_:1},8,["modelValue"])]),_:1}),r.courseForm.courseId?((0,i.uX)(),(0,i.Wv)(y,{key:0,label:"上传视频"},{default:(0,i.k6)(()=>[(0,i.bF)(z,{action:`/courseuploadVideo?courseId=${r.courseForm.courseId}`,"on-success":F.handleUploadSuccess,"before-upload":F.beforeUpload,"show-file-list":!0,multiple:"","file-list":r.videoFileList},{default:(0,i.k6)(()=>[(0,i.bF)(_,{size:"small",type:"primary"},{default:(0,i.k6)(()=>t[18]||(t[18]=[(0,i.eW)("点击上传")])),_:1,__:[18]})]),_:1},8,["action","on-success","before-upload","file-list"]),r.courseForm.videoPath?((0,i.uX)(),(0,i.CE)("div",b,[t[20]||(t[20]=(0,i.eW)(" 已上传视频： ")),((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(r.courseForm.videoPath.split(","),(e,s)=>((0,i.uX)(),(0,i.CE)("span",{key:s},[(0,i.eW)((0,l.v_)(e),1),t[19]||(t[19]=(0,i.Lk)("br",null,null,-1))]))),128))])):((0,i.uX)(),(0,i.CE)("span",v,"没有上传视频"))]),_:1})):(0,i.Q3)("",!0)]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),(0,i.bF)(U,{title:"提示",modelValue:r.deleteDialogVisible,"onUpdate:modelValue":t[11]||(t[11]=e=>r.deleteDialogVisible=e),width:"20%"},{footer:(0,i.k6)(()=>[(0,i.bF)(_,{onClick:t[10]||(t[10]=e=>r.deleteDialogVisible=!1)},{default:(0,i.k6)(()=>t[23]||(t[23]=[(0,i.eW)("取消")])),_:1,__:[23]}),(0,i.bF)(_,{type:"primary",onClick:F.confirmDelete},{default:(0,i.k6)(()=>t[24]||(t[24]=[(0,i.eW)("确定")])),_:1,__:[24]},8,["onClick"])]),default:(0,i.k6)(()=>[t[25]||(t[25]=(0,i.Lk)("span",null,"确定要删除该课程吗？",-1))]),_:1,__:[25]},8,["modelValue"])])}s(8111),s(1701);var k=s(4373),y=s(8626),_={name:"CourseManage",data(){return{searchForm:{title:""},courseList:[],pageIndex:1,pageNumber:10,total:0,isLoading:!0,dialogVisible:!1,dialogTitle:"",courseForm:{courseId:null,userId:1,title:"",description:"",price:0,status:"draft",videoPath:"",imagePath:""},previewImageUrl:"",pendingImage:null,videoFileList:[],rules:{title:[{required:!0,message:"请输入课程标题",trigger:"blur"}],price:[{type:"number",message:"价格必须为数字",trigger:"blur"},{required:!0,message:"请输入价格",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]},deleteDialogVisible:!1,deleteCourseId:null}},mounted(){this.fetchCourses(),this.handleResize=(0,y.throttle)(this.handleResize,100,{leading:!0,trailing:!0}),window.addEventListener("resize",this.handleResize),this.applyResizeObserverPatch()},beforeUnmount(){window.removeEventListener("resize",this.handleResize)},methods:{handleResize(){console.log("Window resized"),this.$nextTick(()=>{this.$refs.table&&this.$refs.table.doLayout()})},handleTableResize(){console.log("Table resized")},applyResizeObserverPatch(){if("undefined"!==typeof window.ResizeObserver){const e=window.ResizeObserver;window.ResizeObserver=class extends e{constructor(e){super((t,s)=>{requestAnimationFrame(()=>{try{e(t,s)}catch(a){console.warn("ResizeObserver callback error:",a)}})})}}}window.addEventListener("error",e=>{e.message.includes("ResizeObserver loop completed with undelivered notifications")&&(console.log("Suppressed ResizeObserver error"),e.preventDefault())})},async fetchCourses(){this.isLoading=!0;try{const e=await k.A.get("/courselist",{params:{pageIndex:this.pageIndex,pageNumber:this.pageNumber,title:this.searchForm.title},withCredentials:!0});console.log("Course list response:",e.data),this.courseList=(e.data.list||[]).map(e=>({...e,title:e.title||"",description:e.description||"",price:void 0!==e.price?e.price:0,status:e.status||"draft",imagePath:e.imagePath||"",videoPath:e.videoPath||""})),this.total=Number(e.data.total)||0,this.pageIndex=Number(e.data.pageIndex)||1,this.pageNumber=Number(e.data.pageNumber)||10}catch(e){this.handleError(e,"获取课程列表失败")}finally{this.isLoading=!1}},searchCourses(){this.pageIndex=1,this.fetchCourses()},handleSizeChange(e){console.log("Page size changed:",e),this.pageNumber=e,this.fetchCourses()},handleCurrentChange(e){console.log("Page index changed:",e),this.pageIndex=e,this.fetchCourses()},handleAdd(){this.dialogTitle="添加课程",this.courseForm={courseId:null,userId:1,title:"",description:"",price:0,status:"draft",videoPath:"",imagePath:""},this.previewImageUrl="",this.pendingImage=null,this.videoFileList=[],this.$refs.courseForm?.resetFields(),this.dialogVisible=!0},handleEdit(e){this.dialogTitle="编辑课程",this.courseForm={...e,price:void 0!==e.price?e.price:0,imagePath:e.imagePath||"",videoPath:e.videoPath||""},this.previewImageUrl="",this.pendingImage=null,this.videoFileList=e.videoPath?e.videoPath.split(",").map((e,t)=>({name:`视频${t+1}`,url:e})):[],this.dialogVisible=!0},async saveCourse(){this.$refs.courseForm.validate(async e=>{if(!e)return;const t=this.courseForm.courseId?"/courseupdate":"/courseadd";try{const e=await k.A.post(t,this.courseForm,{withCredentials:!0});if(console.log("Save course response:",e.data),"OK"===e.data.ok){this.$message.success(this.courseForm.courseId?"修改成功":"添加成功");const t=this.courseForm.courseId||e.data.data?.courseId;if(!t)return this.$message.error("无法获取课程ID，图片上传失败"),this.dialogVisible=!1,void this.fetchCourses();if(this.courseForm.courseId=t,this.pendingImage){const e=new FormData;e.append("file",this.pendingImage),e.append("courseId",t);try{const t=await k.A.post("/courseuploadImage",e,{withCredentials:!0,headers:{"Content-Type":"multipart/form-data"}});console.log("Image upload response:",t.data),"OK"===t.data.ok?(this.courseForm.imagePath=t.data.message,this.$message.success("图片上传成功")):this.$message.error(t.data.message||"图片上传失败")}catch(s){this.handleError(s,"图片上传失败")}}this.dialogVisible=!1,this.previewImageUrl="",this.pendingImage=null,this.videoFileList=[],this.fetchCourses()}else this.$message.error(e.data.message||"操作失败，请重试")}catch(s){this.handleError(s,"保存课程失败")}})},beforeUpload(e){const t="video/mp4"===e.type,s=e.size/1024/1024<500;return t||this.$message.error("仅支持MP4格式视频！"),s||this.$message.error("视频大小不能超过500MB！"),t&&s},handleUploadSuccess(e,t){if(console.log("Video upload response:",e),"OK"===e.ok){const s=e.message;this.courseForm.videoPath=this.courseForm.videoPath?`${this.courseForm.videoPath},${s}`:s,this.$message.success(`视频 ${t.name} 上传成功`)}else this.$message.error(e.message||`视频 ${t.name} 上传失败`)},beforeImageUpload(e){const t=["image/jpeg","image/png"].includes(e.type),s=e.size/1024/1024<10;return t||this.$message.error("仅支持JPG或PNG格式图片！"),s||this.$message.error("图片大小不能超过10MB！"),t&&s},handleImageChange(e){this.pendingImage=e.raw;const t=new FileReader;t.onload=e=>{this.previewImageUrl=e.target.result},t.readAsDataURL(e.raw)},customImageUpload(){this.$message.info("图片将在保存课程后上传")},handleDelete(e){this.deleteCourseId=e,this.deleteDialogVisible=!0},async confirmDelete(){try{const e=await k.A.post("/coursedelete",{courseId:this.deleteCourseId},{withCredentials:!0});"OK"===e.data.ok?(this.$message.success("删除成功"),this.deleteDialogVisible=!1,this.fetchCourses()):this.$message.error(e.data.message||"删除失败，请重试")}catch(e){this.handleError(e,"删除课程失败")}},formatPrice(e){return void 0===e||null===e?"0.00":Number(e).toFixed(2)},handleError(e,t){console.error(e.response||e),this.$message.error(e.response?.data?.message||t)}}},w=s(1241);const C=(0,w.A)(_,[["render",F],["__scopeId","data-v-33287240"]]);var I=C,P={name:"App",components:{CourseManage:I}};const V=(0,w.A)(P,[["render",r]]);var z=V,x=s(3129);s(4188);console.log("main.js loaded");const L=(0,a.Ef)(z);k.A.defaults.baseURL="http://localhost:8080",k.A.defaults.withCredentials=!0,L.config.globalProperties.$axios=k.A,L.use(x.A),L.mount("#my-app"),console.log("Vue app mounted")}},t={};function s(a){var i=t[a];if(void 0!==i)return i.exports;var o=t[a]={id:a,loaded:!1,exports:{}};return e[a].call(o.exports,o,o.exports,s),o.loaded=!0,o.exports}s.m=e,function(){var e=[];s.O=function(t,a,i,o){if(!a){var r=1/0;for(u=0;u<e.length;u++){a=e[u][0],i=e[u][1],o=e[u][2];for(var l=!0,n=0;n<a.length;n++)(!1&o||r>=o)&&Object.keys(s.O).every(function(e){return s.O[e](a[n])})?a.splice(n--,1):(l=!1,o<r&&(r=o));if(l){e.splice(u--,1);var d=i();void 0!==d&&(t=d)}}return t}o=o||0;for(var u=e.length;u>0&&e[u-1][2]>o;u--)e[u]=e[u-1];e[u]=[a,i,o]}}(),function(){s.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return s.d(t,{a:t}),t}}(),function(){s.d=function(e,t){for(var a in t)s.o(t,a)&&!s.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}}(),function(){s.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){s.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){s.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){s.nmd=function(e){return e.paths=[],e.children||(e.children=[]),e}}(),function(){var e={524:0};s.O.j=function(t){return 0===e[t]};var t=function(t,a){var i,o,r=a[0],l=a[1],n=a[2],d=0;if(r.some(function(t){return 0!==e[t]})){for(i in l)s.o(l,i)&&(s.m[i]=l[i]);if(n)var u=n(s)}for(t&&t(a);d<r.length;d++)o=r[d],s.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return s.O(u)},a=self["webpackChunkedu_online_sys"]=self["webpackChunkedu_online_sys"]||[];a.forEach(t.bind(null,0)),a.push=t.bind(null,a.push.bind(a))}();var a=s.O(void 0,[504],function(){return s(6909)});a=s.O(a)})();