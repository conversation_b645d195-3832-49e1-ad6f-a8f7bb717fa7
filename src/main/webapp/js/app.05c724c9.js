(function(){"use strict";var e={2816:function(e,t,n){var a=n(5130),o=n(6768);const r={id:"my-app"};function i(e,t,n,a,i,u){const l=(0,o.g2)("StudentList");return(0,o.uX)(),(0,o.CE)("div",r,[(0,o.bF)(l)])}var u=n(4232);const l={key:0};function s(e,t,n,a,r,i){const s=(0,o.g2)("el-table-column"),d=(0,o.g2)("el-table"),p=(0,o.g2)("el-pagination");return(0,o.uX)(),(0,o.CE)("div",null,[0===r.tableData.length?((0,o.uX)(),(0,o.CE)("div",l,"加载中...")):((0,o.uX)(),(0,o.Wv)(d,{key:1,data:r.tableData,border:"",style:{width:"100%"}},{default:(0,o.k6)(()=>[(0,o.bF)(s,{prop:"username",label:"用户名"}),(0,o.bF)(s,{prop:"email",label:"邮箱"}),(0,o.bF)(s,{prop:"createtime",label:"创建时间"},{default:(0,o.k6)(e=>[(0,o.eW)((0,u.v_)(i.formatDate(e.row.createtime)),1)]),_:1}),(0,o.bF)(s,{prop:"updatetime",label:"更新时间"},{default:(0,o.k6)(e=>[(0,o.eW)((0,u.v_)(i.formatDate(e.row.updatetime)),1)]),_:1})]),_:1},8,["data"])),(0,o.bF)(p,{"current-page":r.pageIndex,"onUpdate:currentPage":t[0]||(t[0]=e=>r.pageIndex=e),"page-size":r.pageSize,"onUpdate:pageSize":t[1]||(t[1]=e=>r.pageSize=e),"page-sizes":[5,10,20,50],total:r.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:i.handleSizeChange,onCurrentChange:i.handleCurrentChange,style:{"margin-top":"20px"}},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])}var d={name:"StudentList",data(){return{tableData:[],pageIndex:1,pageSize:10,total:0}},mounted(){console.log("StudentList mounted"),this.fetchStudentList()},methods:{fetchStudentList(){console.log("Fetching student list..."),this.$axios.get("/studentList",{params:{pageIndex:this.pageIndex,pageSize:this.pageSize},withCredentials:!0}).then(e=>{console.log("Response:",e.data);const t=e.data;t&&t.list&&(this.tableData=t.list,this.total=Number(t.total),this.pageIndex=Number(t.pageIndex),this.pageSize=Number(t.pageNumber))}).catch(e=>{console.error("获取学员列表失败:",e)})},handleSizeChange(e){this.pageSize=e,this.fetchStudentList()},handleCurrentChange(e){this.pageIndex=e,this.fetchStudentList()},formatDate(e){if(!e)return"";const t=new Date(e);return`${t.getFullYear()}-${(t.getMonth()+1).toString().padStart(2,"0")}-${t.getDate().toString().padStart(2,"0")} ${t.getHours().toString().padStart(2,"0")}:${t.getMinutes().toString().padStart(2,"0")}:${t.getSeconds().toString().padStart(2,"0")}`}}},p=n(1241);const c=(0,p.A)(d,[["render",s],["__scopeId","data-v-29ac8b9f"]]);var g=c,f={name:"App",components:{StudentList:g}};const h=(0,p.A)(f,[["render",i]]);var b=h,S=n(3129),m=(n(4188),n(4373));console.log("main.js loaded");const v=(0,a.Ef)(b);m.A.defaults.baseURL="http://localhost:8080",m.A.defaults.withCredentials=!0,v.config.globalProperties.$axios=m.A,v.use(S.A),v.mount("#my-app"),console.log("Vue app mounted")}},t={};function n(a){var o=t[a];if(void 0!==o)return o.exports;var r=t[a]={exports:{}};return e[a].call(r.exports,r,r.exports,n),r.exports}n.m=e,function(){var e=[];n.O=function(t,a,o,r){if(!a){var i=1/0;for(d=0;d<e.length;d++){a=e[d][0],o=e[d][1],r=e[d][2];for(var u=!0,l=0;l<a.length;l++)(!1&r||i>=r)&&Object.keys(n.O).every(function(e){return n.O[e](a[l])})?a.splice(l--,1):(u=!1,r<i&&(i=r));if(u){e.splice(d--,1);var s=o();void 0!==s&&(t=s)}}return t}r=r||0;for(var d=e.length;d>0&&e[d-1][2]>r;d--)e[d]=e[d-1];e[d]=[a,o,r]}}(),function(){n.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return n.d(t,{a:t}),t}}(),function(){n.d=function(e,t){for(var a in t)n.o(t,a)&&!n.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}}(),function(){n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){n.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){var e={524:0};n.O.j=function(t){return 0===e[t]};var t=function(t,a){var o,r,i=a[0],u=a[1],l=a[2],s=0;if(i.some(function(t){return 0!==e[t]})){for(o in u)n.o(u,o)&&(n.m[o]=u[o]);if(l)var d=l(n)}for(t&&t(a);s<i.length;s++)r=i[s],n.o(e,r)&&e[r]&&e[r][0](),e[r]=0;return n.O(d)},a=self["webpackChunkedu_online_sys"]=self["webpackChunkedu_online_sys"]||[];a.forEach(t.bind(null,0)),a.push=t.bind(null,a.push.bind(a))}();var a=n.O(void 0,[504],function(){return n(2816)});a=n.O(a)})();