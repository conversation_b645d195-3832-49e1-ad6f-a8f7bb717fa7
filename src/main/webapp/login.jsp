<%--<%@page import="com.edu.eduOnlineSystem.enums.UserEnum"%>--%>
<%@ page language="java" import="java.util.*" pageEncoding="utf-8" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统登录 - 曹晨曦的在大学通拍卖网后台管理系统</title>
    <script src="https://libs.baidu.com/jquery/1.7.2/jquery.min.js"></script>
    <script type="text/javascript" src="js/login.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', 'Helvetica', sans-serif;
            line-height: 1.6;
            background: url('img/auction-bg.jpg') no-repeat center center fixed;
            background-size: cover;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        /* 背景图半透明遮罩，增强卡片可读性 */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5); /* 深色遮罩 */
            z-index: 1;
        }

        .login-container {
            position: relative;
            z-index: 2;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            padding: 20px;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95); /* 略透明的白色卡片 */
            border-radius: 10px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
            width: 100%;
            max-width: 420px;
            padding: 40px;
            text-align: center;
        }

        .login-header h1 {
            font-size: 22px;
            color: #1a1a1a;
            margin-bottom: 8px;
            word-wrap: break-word; /* 标题自动换行 */
            line-height: 1.3;
            font-weight: 700;
        }

        .login-header p {
            font-size: 16px;
            color: #b8860b; /* 金色，体现拍卖高端感 */
            font-weight: 500;
        }

        .login-form {
            margin-top: 25px;
        }

        .input-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .input-group label {
            display: block;
            font-size: 14px;
            color: #333;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .input-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ccc;
            border-radius: 5px;
            font-size: 14px;
            background: #f9f9f9;
            transition: border-color 0.3s, box-shadow 0.3s;
        }

        .input-group input:focus {
            border-color: #b8860b; /* 金色边框 */
            box-shadow: 0 0 8px rgba(184, 134, 11, 0.2);
            outline: none;
        }

        .captcha-group {
            display: flex;
            flex-direction: column;
        }

        .captcha {
            display: flex;
            align-items: center;
            margin-top: 10px;
        }

        .captcha img {
            border-radius: 4px;
            margin-right: 12px;
            border: 1px solid #ddd;
        }

        .captcha a {
            font-size: 12px;
            color: #b8860b;
            text-decoration: none;
        }

        .captcha a:hover {
            text-decoration: underline;
        }

        .form-actions {
            display: flex;
            justify-content: space-between;
            gap: 12px;
            margin-top: 25px;
        }

        .form-actions button {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.1s;
        }

        .form-actions button[type="submit"] {
            background: #b8860b; /* 金色按钮 */
            color: #fff;
        }

        .form-actions button[type="reset"] {
            background: #4a4a4a; /* 深灰色重置按钮 */
            color: #fff;
        }

        .form-actions button:hover {
            opacity: 0.9;
        }

        .form-actions button:active {
            transform: scale(0.98);
        }

        @media (max-width: 480px) {
            .login-card {
                padding: 25px;
                max-width: 90%;
            }

            .login-header h1 {
                font-size: 18px; /* 小屏幕标题更小 */
            }

            .login-header p {
                font-size: 14px;
            }

            .form-actions {
                flex-direction: column;
                gap: 10px;
            }

            .form-actions button {
                width: 100%;
            }
        }
    </style>

    <script type="text/javascript">
        // 当页面加载完成后执行
        window.onload = function () {
            // 使用URLSearchParams API来获取URL参数，这是现代、安全的方式
            const urlParams = new URLSearchParams(window.location.search);
            const msg = urlParams.get('msg');

            if (msg) {
                let alertMessage = '';
                if (msg === 'LOGIN_FAIL') {
                    alertMessage = '登录失败：用户名或密码错误！';
                } else if (msg === 'CODE_ERROR') {
                    alertMessage = '验证码错误！';
                }

                if (alertMessage) {
                    alert(alertMessage);
                }
            }
        };
    </script>
</head>
<body>
<div class="login-container">
    <div class="login-card">
        <header class="login-header">
            <h1>曹晨曦的在大学通拍卖网后台管理系统</h1>
            <p>系统登录</p>
        </header>
        <form class="login-form" action="login" method="post">
            <div class="input-group">
                <label for="user">邮箱</label>
                <input id="user" type="email" name="email" value="<EMAIL>" placeholder="请输入邮箱"
                       required/>
            </div>
            <div class="input-group">
                <label for="mima">密码</label>
                <input id="mima" type="password" name="password" value="123456" placeholder="请输入密码" required/>
            </div>
            <div class="input-group captcha-group">
                <label for="passwords">验证码</label>
                <input type="text" name="usercode" id="passwords" placeholder="请输入验证码" required/>
                <div class="captcha">
                    <img id="validateCode" src="/number.jsp" width="96" height="36" alt="验证码"/>
                    <a id="changeCode" href="javascript:void(0);" title="看不清，换一张">换一张</a>
                </div>
            </div>
            <div class="form-actions">
                <button type="submit">登录</button>
                <button type="reset">重置</button>
            </div>
        </form>
    </div>
</div>
</body>
</html>