<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>欢迎 - 曹晨曦的在大学通拍卖网后台管理系统</title>
    <script src="https://libs.baidu.com/jquery/1.7.2/jquery.min.js"></script>
    <script type="text/javascript" src="js/time.js"></script>
    <link rel="stylesheet" href="css/public.css">

    <style>
        /* 欢迎弹窗样式 */
        .welcome-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            animation: fadeIn 0.3s ease-in-out;
        }

        .welcome-modal-content {
            background-color: #fff;
            margin: 10% auto;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            width: 90%;
            max-width: 500px;
            text-align: center;
            position: relative;
            animation: slideIn 0.3s ease-out;
        }

        .welcome-modal-header {
            margin-bottom: 20px;
        }

        .welcome-modal-header h2 {
            color: #b8860b;
            font-size: 24px;
            margin-bottom: 10px;
        }

        .welcome-modal-body {
            margin-bottom: 25px;
        }

        .welcome-modal-body p {
            font-size: 16px;
            color: #333;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        .user-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }

        .user-info strong {
            color: #b8860b;
        }

        .welcome-modal-footer {
            text-align: center;
        }

        .btn-close {
            background: #b8860b;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .btn-close:hover {
            background: #9a7209;
        }

        .close-x {
            position: absolute;
            right: 15px;
            top: 15px;
            font-size: 24px;
            font-weight: bold;
            color: #aaa;
            cursor: pointer;
            transition: color 0.3s;
        }

        .close-x:hover {
            color: #000;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 主页面样式 */
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #b8860b, #daa520);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 28px;
        }

        .user-welcome {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .time-display {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            margin-bottom: 20px;
        }

        .nav-menu {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .nav-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
        }

        .nav-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
        }

        .nav-item h3 {
            color: #b8860b;
            margin-bottom: 10px;
        }

        .logout-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            float: right;
            margin-top: 10px;
        }

        .logout-btn:hover {
            background: #c82333;
        }
    </style>
</head>
<body>
    <!-- 主页面内容 -->
    <div class="main-container">
        <div class="header">
            <h1>曹晨曦的在大学通拍卖网后台管理系统</h1>
            <button class="logout-btn" onclick="logout()">退出登录</button>
        </div>

        <div class="user-welcome">
            <h2>欢迎回来！</h2>
            <div class="time-display">
                <div id="greeting">您好</div>
                <div id="time"></div>
            </div>

            <c:if test="${not empty user}">
                <div class="user-info">
                    <p><strong>用户名：</strong>${user.username}</p>
                    <p><strong>邮箱：</strong>${user.email}</p>
                    <p><strong>注册时间：</strong>${user.createtime}</p>
                </div>
            </c:if>

            <!-- 如果没有从controller传递user，则从session获取 -->
            <c:if test="${empty user and not empty sessionScope.loggedInUser}">
                <div class="user-info">
                    <p><strong>用户名：</strong>${sessionScope.loggedInUser.username}</p>
                    <p><strong>邮箱：</strong>${sessionScope.loggedInUser.email}</p>
                    <p><strong>注册时间：</strong>${sessionScope.loggedInUser.createtime}</p>
                </div>
            </c:if>
        </div>

        <div class="nav-menu">
            <div class="nav-item">
                <h3>拍卖管理</h3>
                <p>管理拍卖商品和拍卖活动</p>
            </div>
            <div class="nav-item">
                <h3>用户管理</h3>
                <p>管理系统用户信息</p>
            </div>
            <div class="nav-item">
                <h3>订单管理</h3>
                <p>查看和管理拍卖订单</p>
            </div>
            <div class="nav-item">
                <h3>系统设置</h3>
                <p>系统配置和参数设置</p>
            </div>
        </div>
    </div>

    <!-- 欢迎弹窗 -->
    <c:if test="${showWelcomeModal}">
        <div id="welcomeModal" class="welcome-modal">
            <div class="welcome-modal-content">
                <span class="close-x" onclick="closeWelcomeModal()">&times;</span>
                <div class="welcome-modal-header">
                    <h2>🎉 登录成功！</h2>
                </div>
                <div class="welcome-modal-body">
                    <p>欢迎来到曹晨曦的在大学通拍卖网后台管理系统！</p>
                    <c:if test="${not empty user}">
                        <div class="user-info">
                            <p><strong>欢迎您：</strong>${user.username}</p>
                            <p><strong>登录邮箱：</strong>${user.email}</p>
                        </div>
                    </c:if>
                    <c:if test="${empty user and not empty sessionScope.loggedInUser}">
                        <div class="user-info">
                            <p><strong>欢迎您：</strong>${sessionScope.loggedInUser.username}</p>
                            <p><strong>登录邮箱：</strong>${sessionScope.loggedInUser.email}</p>
                        </div>
                    </c:if>
                    <p>您可以开始使用系统的各项功能了。</p>
                </div>
                <div class="welcome-modal-footer">
                    <button class="btn-close" onclick="closeWelcomeModal()">开始使用</button>
                </div>
            </div>
        </div>
    </c:if>

    <script>
        // 显示欢迎弹窗
        <c:if test="${showWelcomeModal}">
            $(document).ready(function() {
                $('#welcomeModal').show();
            });
        </c:if>

        // 关闭欢迎弹窗
        function closeWelcomeModal() {
            $('#welcomeModal').fadeOut(300);
        }

        // 点击弹窗外部关闭
        $(document).on('click', '.welcome-modal', function(e) {
            if (e.target === this) {
                closeWelcomeModal();
            }
        });

        // ESC键关闭弹窗
        $(document).keydown(function(e) {
            if (e.keyCode === 27) { // ESC键
                closeWelcomeModal();
            }
        });

        // 退出登录
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                window.location.href = '/logout';
            }
        }
    </script>
</body>
</html>