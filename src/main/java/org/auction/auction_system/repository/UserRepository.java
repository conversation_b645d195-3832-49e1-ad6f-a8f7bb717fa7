package org.auction.auction_system.repository;

import org.auction.auction_system.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, String> {

    // Spring Data JPA会根据方法名自动生成查询
    // "根据用户名查找用户"
    Optional<User> findByUsername(String username);
}