package org.auction.auction_system.controller;

import jakarta.servlet.http.HttpSession;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Random;

@Controller
public class CaptchaController {

    // 您的JSP的img src指向的应该是这个路径
    // 为了简化，这里不生成图片，只生成一个随机数并存入session
    // 在真实项目中，这里会生成图片并写入response
    @GetMapping("/generateCaptcha")
    @ResponseBody // 直接返回文本
    public String generateCaptcha(HttpSession session) {
        String captcha = String.format("%04d", new Random().nextInt(9999)); // 生成4位随机数
        session.setAttribute("captcha", captcha);
        // 实际项目中这里会返回图片流
        // 为了方便测试，我们直接返回验证码文本，您可以在浏览器中看到它
        return "验证码 (用于测试): " + captcha;
    }
}