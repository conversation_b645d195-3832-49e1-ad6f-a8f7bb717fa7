package org.auction.auction_system.controller;

import jakarta.servlet.http.HttpSession;
import org.auction.auction_system.entity.User;
import org.auction.auction_system.repository.UserRepository;
import org.auction.auction_system.service.UserService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Controller
public class LoginController {

    private final UserService userService;
    private final UserRepository userRepository; // 用于登录成功后获取完整用户信息

    public LoginController(UserService userService, UserRepository userRepository) {
        this.userService = userService;
        this.userRepository = userRepository;
    }

    // 1. 显示登录页面
    @GetMapping({"/", "/login"}) // 访问根路径或/login都显示登录页
    public String showLoginPage() {
        return "login"; // 返回 /WEB-INF/views/login.jsp
    }

    // 2. 处理登录表单提交
    @PostMapping("/login")
    public String handleLogin(
            @RequestParam("email") String email,
            @RequestParam("password") String password,
            @RequestParam("usercode") String userCode,
            HttpSession session) {

        String sessionCode = (String) session.getAttribute("syscode");

        // 验证验证码
        if (sessionCode == null || !sessionCode.equalsIgnoreCase(userCode)) {
            return "redirect:/login?msg=CODE_ERROR";
        }

        // 根据邮箱查找用户并验证密码
        User user = userService.validateLogin(email, password);
        if (user == null) {
            return "redirect:/login?msg=LOGIN_FAIL";
        }

        // 登录成功
        session.setAttribute("loggedInUser", user);
        // 重定向到登录页面并显示成功消息
        return "redirect:/login?msg=LOGIN_SUCCESS";
    }


    // 4. 登出
    @GetMapping("/logout")
    public String logout(HttpSession session) {
        session.invalidate(); //销毁session
        return "redirect:/login";
    }
}