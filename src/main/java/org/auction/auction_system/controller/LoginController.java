package org.auction.auction_system.controller;

import jakarta.servlet.http.HttpSession;
import org.auction.auction_system.entity.User;
import org.auction.auction_system.repository.UserRepository;
import org.auction.auction_system.service.UserService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Controller
public class LoginController {

    private final UserService userService;
    private final UserRepository userRepository; // 用于登录成功后获取完整用户信息

    public LoginController(UserService userService, UserRepository userRepository) {
        this.userService = userService;
        this.userRepository = userRepository;
    }

    // 1. 显示登录页面
    @GetMapping({"/", "/login"}) // 访问根路径或/login都显示登录页
    public String showLoginPage() {
        return "login"; // 返回 /WEB-INF/views/login.jsp
    }

    // 2. 处理登录表单提交
    @PostMapping("/login")
    public String handleLogin(
            @RequestParam("email") String email, // 对应JSP中的name="email"
            @RequestParam("password") String password,
            @RequestParam("usercode") String userCode,
            HttpSession session) {

        String sessionCode = (String) session.getAttribute("syscode");

        // 验证验证码
        if (sessionCode == null || !sessionCode.equalsIgnoreCase(userCode)) {
            return "redirect:/login?msg=CODE_ERROR";
        }

        // 根据邮箱查找用户并验证密码
        User user = userService.validateLogin(email, password);
        if (user == null) {
            return "redirect:/login?msg=LOGIN_FAIL";
        }

        // 登录成功
        session.setAttribute("loggedInUser", user);
        session.setAttribute("showWelcomeModal", true); // 标记显示欢迎弹窗
        return "redirect:/welcome"; // 重定向到成功页面
    }

    // 3. 登录成功后的欢迎页面
    @GetMapping("/welcome")
    public String showWelcomePage(HttpSession session, Model model) {
        // 防止未登录用户直接访问
        User loggedInUser = (User) session.getAttribute("loggedInUser");
        if (loggedInUser == null) {
            return "redirect:/login";
        }

        // 将用户信息传递给页面
        model.addAttribute("user", loggedInUser);

        // 检查是否需要显示欢迎弹窗
        Boolean showModal = (Boolean) session.getAttribute("showWelcomeModal");
        if (showModal != null && showModal) {
            model.addAttribute("showWelcomeModal", true);
            session.removeAttribute("showWelcomeModal"); // 移除标记，避免重复显示
        }

        return "welcome"; // 返回 /WEB-INF/views/welcome.jsp
    }

    // 4. 登出
    @GetMapping("/logout")
    public String logout(HttpSession session) {
        session.invalidate(); //销毁session
        return "redirect:/login";
    }
}