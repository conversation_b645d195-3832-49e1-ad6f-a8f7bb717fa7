/**
 * Spring Boot 应用程序的主入口类。
 * 此类负责初始化应用程序，并支持以 WAR 文件形式部署到外部 Servlet 容器。
 * 它利用 Spring Boot 的自动配置功能，并继承 SpringBootServletInitializer 以支持 WAR 部署。
 *
 * <AUTHOR>
 * @since 0.0.1-SNAPSHOT
 */
package org.auction.auction_system; // 定义应用程序入口类的包名

import org.springframework.boot.SpringApplication; // 导入 Spring Boot 应用程序启动器
import org.springframework.boot.autoconfigure.SpringBootApplication; // 导入 Spring Boot 自动配置注解
import org.springframework.boot.builder.SpringApplicationBuilder; // 导入用于自定义 Spring Boot 应用程序的构建器
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer; // 导入支持 WAR 部署的基类

/**
 * Spring Boot 应用程序的主类，作为应用程序的入口点。
 * 使用 @SpringBootApplication 注解启用自动配置、组件扫描和配置属性支持。
 * 继承 SpringBootServletInitializer 以支持在外部 Servlet 容器（如 Tomcat）中以 WAR 文件部署。
 */
@SpringBootApplication
public class Application extends SpringBootServletInitializer {

    /**
     * 配置 Spring Boot 应用程序以支持在外部 Servlet 容器中以 WAR 文件部署。
     * 当应用程序以 WAR 文件部署时，此方法会被调用，允许自定义应用程序上下文。
     *
     * @param application 用于配置应用程序的 SpringApplicationBuilder 实例
     * @return 配置好的 SpringApplicationBuilder 实例
     */
    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(Application.class); // 指定应用程序上下文的主要源
    }

    /**
     * 应用程序的主方法，作为 Spring Boot 应用程序的入口点。
     * 通过 SpringApplication.run 启动应用程序，初始化 Spring 上下文并启动嵌入式服务器。
     *
     * @param args 命令行参数，传递给应用程序
     */
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args); // 运行 Spring Boot 应用程序
    }
}