package org.auction.auction_system.dto;

/**
 * 登录请求数据传输对象
 */
public class LoginRequest {
    private String email;      // 邮箱（用户名）
    private String password;   // 密码
    private String usercode;   // 验证码

    public LoginRequest() {
    }

    public LoginRequest(String email, String password, String usercode) {
        this.email = email;
        this.password = password;
        this.usercode = usercode;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getUsercode() {
        return usercode;
    }

    public void setUsercode(String usercode) {
        this.usercode = usercode;
    }

    @Override
    public String toString() {
        return "LoginRequest{" +
                "email='" + email + '\'' +
                ", password='[PROTECTED]'" +
                ", usercode='" + usercode + '\'' +
                '}';
    }
}
