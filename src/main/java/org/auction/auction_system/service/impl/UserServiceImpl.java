package org.auction.auction_system.service.impl;

import org.auction.auction_system.entity.User;
import org.auction.auction_system.repository.UserRepository;
import org.auction.auction_system.service.UserService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class UserServiceImpl implements UserService {

    private final UserRepository userRepository;

    // 构造函数注入
    public UserServiceImpl(UserRepository userRepository) {
        this.userRepository = userRepository;
    }

    @Override
    public String login(String username, String password, String sessionCode, String userCode) {
        // 1. 验证码校验
        if (sessionCode == null || !sessionCode.equalsIgnoreCase(userCode)) {
            return "CODE_ERROR"; // 返回验证码错误代码
        }

        // 2. 根据用户名查找用户
        Optional<User> userOptional = userRepository.findByUsername(username);
        if (userOptional.isEmpty()) {
            return "LOGIN_FAIL"; // 用户不存在
        }

        User user = userOptional.get();

        // 3. 【不安全的】明文密码比较
        if (!user.getPassword().equals(password)) {
            return "LOGIN_FAIL"; // 密码错误
        }

        return "SUCCESS"; // 登录成功
    }

    @Override
    public List<User> getAllUsers() {
        return userRepository.findAll();
    }

    @Override
    public User findByEmail(String email) {
        // 首先尝试通过email字段查找
        Optional<User> userOpt = userRepository.findByEmail(email);
        if (userOpt.isPresent()) {
            return userOpt.get();
        }

        // 如果email字段没找到，尝试通过username字段查找（兼容性）
        userOpt = userRepository.findByUsername(email);
        return userOpt.orElse(null);
    }

    @Override
    public User validateLogin(String email, String password) {
        User user = findByEmail(email);
        if (user != null && user.getPassword().equals(password)) {
            return user;
        }
        return null;
    }
}