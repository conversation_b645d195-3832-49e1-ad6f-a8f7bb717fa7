package org.auction.auction_system.service;

import org.auction.auction_system.entity.User;

import java.util.List;

public interface UserService {
    // 返回一个消息代码，而不是抛出异常
    String login(String username, String password, String sessionCode, String userCode);

    List<User> getAllUsers();

    /**
     * 根据邮箱查找用户
     * @param email 邮箱
     * @return 用户对象，如果不存在返回null
     */
    User findByEmail(String email);

    /**
     * 验证用户登录
     * @param email 邮箱
     * @param password 密码
     * @return 如果验证成功返回用户对象，否则返回null
     */
    User validateLogin(String email, String password);
}